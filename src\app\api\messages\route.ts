import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { NotificationService } from '@/lib/services/notificationService';
import connectDB from '@/lib/db/mongodb';
import { ConversationDocument } from '@/lib/db/models/interfaces';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const conversationId = searchParams.get('conversationId');
    
    if (!conversationId) {
      return NextResponse.json(
        { success: false, error: 'conversationId is required' },
        { status: 400 }
      );
    }
    
    // Verify user has access to this conversation
    const conversation = await DataService.getConversationById(conversationId);
    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      );
    }
    
    // Check if user is a participant in the conversation
    const isParticipant = (conversation as unknown as ConversationDocument).participants.some(
      (participant) => participant.toString() === request.user!.id
    );
    
    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      );
    }
    
    const messages = await DataService.getMessages(conversationId);
    
    return NextResponse.json({
      success: true,
      data: messages
    });
  } catch (error) {
    console.error('Messages GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    const messageData = await request.json();
    
    if (!messageData.conversationId || !messageData.content) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Verify user has access to this conversation
    const conversation = await DataService.getConversationById(messageData.conversationId);
    if (!conversation) {
      return NextResponse.json(
        { success: false, error: 'Conversation not found' },
        { status: 404 }
      );
    }
    
    // Check if user is a participant in the conversation
    const isParticipant = (conversation as unknown as ConversationDocument).participants.some(
      (participant) => participant.toString() === request.user!.id
    );
    
    if (!isParticipant) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this conversation' },
        { status: 403 }
      );
    }
    
    // Sanitize message content
    const sanitizedContent = messageData.content.replace(/<script[^>]*>.*?<\/script>/gi, '').replace(/<[^>]*>/g, '').trim();
    
    if (!sanitizedContent) {
      return NextResponse.json(
        { success: false, error: 'Message content cannot be empty' },
        { status: 400 }
      );
    }
    
    // Ensure senderId matches authenticated user
    const message = await DataService.createMessage({
      ...messageData,
      content: sanitizedContent,
      senderId: request.user!.id
    });

    // Create notifications for other participants in the conversation
    try {
      const conversationWithParticipants = conversation as unknown as ConversationDocument;
      
      const otherParticipants = conversationWithParticipants.participants.filter(
        (participant) => participant.toString() !== request.user!.id
      );

      // Get sender info
      const sender = await DataService.getUserById(request.user!.id);
      const senderName = sender ? `${sender.firstName} ${sender.lastName}` : 'Someone';

      // Get report title if this conversation is linked to a report
      let reportTitle: string | undefined;
      if (conversationWithParticipants.reportId) {
        const report = await DataService.getReportById(conversationWithParticipants.reportId.toString());
        reportTitle = report?.title;
      }

      // Create notification for each other participant
      for (const participant of otherParticipants) {
        await NotificationService.createNewMessageNotification(
          participant.toString(),
          senderName,
          messageData.conversationId,
          reportTitle,
          messageData.content
        );
      }
    } catch (notificationError) {
      console.error('Failed to create message notification:', notificationError);
      // Don't fail the message creation if notification fails
    }
    
    return NextResponse.json({
      success: true,
      data: message
    }, { status: 201 });
  } catch (error) {
    console.error('Messages POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export async function PUT(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const action = searchParams.get('action');
    
    if (!messageId) {
      return NextResponse.json(
        { success: false, error: 'messageId is required' },
        { status: 400 }
      );
    }
    
    const requestData = await request.json();
    
    switch (action) {
      case 'mark_read':
        if (!requestData.userId) {
          return NextResponse.json(
            { success: false, error: 'userId is required' },
            { status: 400 }
          );
        }
        const readMessage = await DataService.markMessageAsRead(messageId, requestData.userId);
        return NextResponse.json({
          success: true,
          data: readMessage
        });
        
      case 'update':
        const sanitizedContent = requestData.content?.replace(/[<>"'&]/g, '').substring(0, 1000) || '';
        const updatedMessage = await DataService.updateMessage(messageId, {
          content: sanitizedContent,
          htmlContent: requestData.htmlContent?.replace(/<script[^>]*>.*?<\/script>/gi, '')
        });
        return NextResponse.json({
          success: true,
          data: updatedMessage
        });
        
      case 'add_reaction':
        if (!requestData.userId || !requestData.emoji) {
          return NextResponse.json(
            { success: false, error: 'userId and emoji are required' },
            { status: 400 }
          );
        }
        const sanitizedEmoji = requestData.emoji?.replace(/[^\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F1E0}-\u{1F1FF}]/gu, '') || '';
        const messageWithReaction = await DataService.addReaction(
          messageId, 
          requestData.userId, 
          sanitizedEmoji
        );
        return NextResponse.json({
          success: true,
          data: messageWithReaction
        });
        
      case 'remove_reaction':
        if (!requestData.userId) {
          return NextResponse.json(
            { success: false, error: 'userId is required' },
            { status: 400 }
          );
        }
        const messageWithoutReaction = await DataService.removeReaction(messageId, requestData.userId);
        return NextResponse.json({
          success: true,
          data: messageWithoutReaction
        });
        
      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }
  } catch {
    console.error('Messages PUT API error');
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const messageId = searchParams.get('messageId');
    const userId = searchParams.get('userId');
    
    if (!messageId || !userId) {
      return NextResponse.json(
        { success: false, error: 'messageId and userId are required' },
        { status: 400 }
      );
    }
    
    const deletedMessage = await DataService.deleteMessage(messageId, userId);
    
    return NextResponse.json({
      success: true,
      data: deletedMessage
    });
  } catch (error) {
    console.error('Messages DELETE API error:', error);
    const sanitizedError = error instanceof Error 
      ? error.message.replace(/[<>"'&]/g, '').substring(0, 100)
      : 'Internal server error';
    
    return NextResponse.json(
      { success: false, error: sanitizedError },
      { status: error instanceof Error && error.message.includes('Unauthorized') ? 403 : 500 }
    );
  }
}