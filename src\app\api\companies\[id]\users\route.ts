import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { withAuth, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';
import { UserDocument } from '@/lib/db/models/interfaces';

export const runtime = 'nodejs';

export const GET = withAuth(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    // Extract company ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const companyId = pathSegments[pathSegments.indexOf('companies') + 1];
    
    // Only admins can view company users, and only for their own company
    if (request.user!.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Only administrators can view company users' },
        { status: 403 }
      );
    }
    
    // Ensure admin can only view users from their own company
    if (request.user!.companyId !== companyId) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this company' },
        { status: 403 }
      );
    }
    
    const users = await DataService.getCompanyUsers(companyId || '');
    
    return NextResponse.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Company users GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { roles: ['admin'] });

export const POST = withAuth(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    // Extract company ID from URL
    const url = new URL(request.url);
    const pathSegments = url.pathname.split('/');
    const companyId = pathSegments[pathSegments.indexOf('companies') + 1];
    const userData = await request.json();
    
    // Only admins can create company users, and only for their own company
    if (request.user!.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Only administrators can create company users' },
        { status: 403 }
      );
    }
    
    // Ensure admin can only create users for their own company
    if (request.user!.companyId !== companyId) {
      return NextResponse.json(
        { success: false, error: 'Access denied to this company' },
        { status: 403 }
      );
    }
    
    if (!userData.email || !userData.role || !userData.firstName || !userData.lastName) {
      return NextResponse.json(
        { success: false, error: 'Email, role, firstName, and lastName are required' },
        { status: 400 }
      );
    }
    
    // Create user with company association
    const newUser = await DataService.createUser({
      ...userData,
      companyId,
      isActive: true,
      // Generate a temporary password that user must change on first login
      password: DataService.generateToken().substring(0, 12) + 'Temp!',
      passwordNeedsMigration: true
    });
    
    return NextResponse.json({
      success: true,
      data: {
        id: newUser._id,
        email: (newUser as unknown as UserDocument).email,
        firstName: (newUser as unknown as UserDocument).firstName,
        lastName: (newUser as unknown as UserDocument).lastName,
        role: (newUser as unknown as UserDocument).role,
        companyId: (newUser as unknown as UserDocument).companyId?.toString(),
        isActive: (newUser as unknown as UserDocument).isActive
      },
      message: 'User created successfully. They will need to reset their password on first login.'
    }, { status: 201 });
  } catch (error) {
    console.error('Company users POST API error:', error);
    
    // Handle duplicate email error
    if (error instanceof Error && error.message.includes('duplicate key error')) {
      return NextResponse.json(
        { success: false, error: 'A user with this email already exists' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}, { roles: ['admin'] });