import connectDB from './mongodb';
import { DataService } from './dataService';

export async function seedDatabase() {
  try {
    await connectDB();
    console.log('Starting database seeding...');

    // Get existing users from database
    const seededUsers = [];
    try {
      // Get all existing users from the User model directly
      const { User } = await import('./models');
      const existingUsers = await User.find({}).select('-hashedPassword');
      seededUsers.push(...existingUsers);
      console.log(`Found ${existingUsers.length} existing users in database`);
    } catch (error) {
      console.error('Error fetching existing users:', error);
    }

    // Create sample reports for testing
    const sampleReports = [];
    const whistleblowerUsers = seededUsers.filter(user => (user as unknown as { role: string }).role === 'whistleblower');
    const adminUsers = seededUsers.filter(user => (user as unknown as { role: string }).role === 'admin');

    if (whistleblowerUsers.length > 0 && adminUsers.length > 0) {
      const sampleReportData = [
        {
          title: 'Financial Irregularities in Accounting Department',
          description: 'I have noticed suspicious financial transactions that appear to be manipulated to hide losses. The accounting department has been reporting inflated revenue figures.',
          category: 'Financial',
          priority: 'High',
          userId: whistleblowerUsers[0]?._id,
          status: 'Under Review',
          isAnonymous: false,
          assignedInvestigator: adminUsers[0]?._id
        },
        {
          title: 'Workplace Safety Violations',
          description: 'Safety protocols are being ignored in the manufacturing floor. Workers are not provided with proper protective equipment.',
          category: 'Workplace Safety',
          priority: 'Critical',
          userId: whistleblowerUsers[0]?._id,
          status: 'New',
          isAnonymous: true
        }
      ];

      for (const reportData of sampleReportData) {
        // Skip reports with undefined userIds
        if (!reportData.userId) {
          console.log(`Skipping report "${reportData.title}" - no valid user ID`);
          continue;
        }

        try {
          const report = await DataService.createReport(reportData as Parameters<typeof DataService.createReport>[0]);
          sampleReports.push(report);
          console.log(`Created sample report: ${report.title}`);
        } catch (error) {
          console.error('Error creating sample report:', error);
        }
      }
    }

    // Create conversations and messages for the reports
    const conversations = [];
    for (const report of sampleReports) {
      try {
        // Create conversation
        const participants = [report.userId.toString()];
        if (report.assignedInvestigator) {
          participants.push(report.assignedInvestigator.toString());
        } else if (adminUsers.length > 0 && adminUsers[0]) {
          participants.push(adminUsers[0]._id.toString());
        }

        const conversation = await DataService.createConversation({
          reportId: report._id.toString(),
          participants
        });

        conversations.push(conversation);
        console.log(`Created conversation for report: ${report.title}`);

        // Create initial messages
        if (adminUsers.length > 0 && adminUsers[0]) {
          await DataService.createMessage({
            conversationId: conversation._id.toString(),
            senderId: adminUsers[0]?._id.toString(),
            content: `Thank you for your report "${report.title}". We have received your submission and will begin our investigation shortly. Your report ID is ${report.reportId}. We will keep you updated on our progress.`,
            messageType: 'text'
          });

          console.log(`Created welcome message for report: ${report.title}`);

          // Create a follow-up message from the whistleblower
          if (whistleblowerUsers.length > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Small delay for different timestamps
            
            await DataService.createMessage({
              conversationId: conversation._id.toString(),
              senderId: report.userId.toString(),
              content: 'Thank you for the quick response. I have additional evidence that I can provide if needed. Please let me know how to proceed.',
              messageType: 'text'
            });

            console.log(`Created follow-up message for report: ${report.title}`);
          }
        }
      } catch (error) {
        console.error(`Error creating conversation for report ${report.title}:`, error);
      }
    }

    console.log('Database seeding completed successfully!');
    return {
      users: seededUsers.length,
      reports: sampleReports.length,
      conversations: conversations.length,
      message: 'Database seeded with users, reports, conversations, and messages'
    };
  } catch (error) {
    console.error('Error in seed function:', error);
    throw error;
  }
}