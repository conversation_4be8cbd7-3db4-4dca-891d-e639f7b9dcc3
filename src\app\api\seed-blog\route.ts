import { NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import Blog from '@/lib/db/models/Blog';

const BLOG_SEED_DATA = [
  {
    image: '/desktop/blog/blog1.jpg',
    category: 'Compliance Updates',
    date: 'January 15, 2024',
    title: 'New Whistleblower Protection Laws: What You Need to Know',
    description: 'Understanding the latest regulatory changes and their impact on corporate compliance programs.',
    author: {
      image: '/desktop/blog/author1.jpg',
      name: 'Dr. <PERSON>',
      initials: '<PERSON><PERSON>'
    },
    readTime: '5 min read',
    slug: 'new-whistleblower-protection-laws',
    featured: true,
    content: `<p>The landscape of whistleblower protection is evolving rapidly, with new regulations coming into effect that significantly impact how organizations handle internal reporting systems.</p>
    
    <h2>Key Changes in 2024</h2>
    <p>The most significant updates include enhanced protection for anonymous reporters, stricter penalties for retaliation, and expanded coverage for contractors and third-party vendors.</p>
    
    <h2>Implementation Requirements</h2>
    <p>Organizations must now provide multiple reporting channels, ensure 24/7 availability, and implement robust case management systems to track and resolve reports effectively.</p>`,
    tags: ['Whistleblower Protection', 'Regulatory Compliance', 'Legal Updates']
  },
  {
    image: '/desktop/blog/blog2.jpg',
    category: 'Best Practices',
    date: 'January 10, 2024',
    title: 'Building an Effective Anonymous Reporting System',
    description: 'Key strategies for implementing secure and trustworthy whistleblower channels.',
    author: {
      image: '/desktop/blog/author2.jpg',
      name: '<PERSON>, CCEP',
      initials: 'JC'
    },
    readTime: '7 min read',
    slug: 'building-effective-anonymous-reporting',
    featured: true,
    content: `<p>Creating a robust anonymous reporting system requires careful consideration of technology, process, and culture.</p>
    
    <h2>Technology Foundation</h2>
    <p>Modern reporting systems must provide multiple channels including web portals, mobile apps, and telephone hotlines, all designed with privacy and security as core principles.</p>
    
    <h2>Building Trust</h2>
    <p>Trust is the cornerstone of any effective reporting system. Organizations must demonstrate their commitment to protecting reporters and taking action on legitimate concerns.</p>`,
    tags: ['Ethics & Governance', 'Risk Management', 'Technology']
  },
  {
    image: '/desktop/blog/blog3.jpg',
    category: 'Case Studies',
    date: 'January 5, 2024',
    title: 'How Fortune 500 Companies Handle Whistleblower Reports',
    description: 'Real-world examples of successful compliance programs and their outcomes.',
    author: {
      image: '/desktop/blog/author3.jpg',
      name: 'Olivia Washington',
      initials: 'OW'
    },
    readTime: '6 min read',
    slug: 'fortune-500-whistleblower-handling',
    featured: true,
    content: `<p>Leading corporations have developed sophisticated approaches to managing whistleblower reports that balance thorough investigation with operational efficiency.</p>
    
    <h2>Case Study: Tech Giant's Approach</h2>
    <p>A major technology company reduced investigation time by 40% while increasing report resolution rates through automated case routing and AI-assisted initial screening.</p>
    
    <h2>Lessons Learned</h2>
    <p>The most successful programs combine advanced technology with human expertise, ensuring that every report receives appropriate attention while maintaining efficiency.</p>`,
    tags: ['Corporate Culture', 'Best Practices', 'Case Studies']
  },
  {
    image: '/desktop/blog/blog4.jpg',
    category: 'Industry Trends',
    date: 'December 28, 2023',
    title: 'The Rise of AI in Compliance Monitoring',
    description: 'How artificial intelligence is transforming compliance and risk management.',
    author: {
      image: '/desktop/blog/author4.jpg',
      name: 'Michael Reeves',
      initials: 'MR'
    },
    readTime: '8 min read',
    slug: 'ai-compliance-monitoring',
    featured: false,
    content: `<p>Artificial intelligence is revolutionizing how organizations monitor compliance and detect potential issues before they escalate.</p>
    
    <h2>Predictive Analytics</h2>
    <p>AI systems can now identify patterns that may indicate compliance risks, enabling proactive intervention rather than reactive responses.</p>`,
    tags: ['Technology', 'AI', 'Risk Management']
  },
  {
    image: '/desktop/blog/blog5.jpg',
    category: 'Company News',
    date: 'December 20, 2023',
    title: 'Expanding Global Compliance Standards',
    description: 'New international frameworks for corporate accountability and transparency.',
    author: {
      image: '/desktop/blog/author5.jpg',
      name: 'Sofia Rodriguez',
      initials: 'SR'
    },
    readTime: '4 min read',
    slug: 'global-compliance-standards',
    featured: false,
    content: `<p>International regulatory bodies are working together to create unified standards for corporate compliance and whistleblower protection.</p>
    
    <h2>Global Impact</h2>
    <p>These new standards will affect multinational corporations operating across different jurisdictions, requiring harmonized compliance approaches.</p>`,
    tags: ['Global Standards', 'Regulatory Compliance', 'International Law']
  }
];

export async function POST() {
  try {
    await connectDB();
    
    // Clear existing blogs
    await Blog.deleteMany({});
    
    // Insert seed data
    const blogs = await Blog.insertMany(BLOG_SEED_DATA);
    
    return NextResponse.json({
      success: true,
      message: `Successfully seeded ${blogs.length} blog posts`,
      data: blogs
    });
  } catch (error) {
    console.error('Error seeding blog data:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to seed blog data' },
      { status: 500 }
    );
  }
}