import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { Bell, MessageSquare, AlertTriangle, Info } from "lucide-react"
import { NotificationType, NotificationPriority } from "@/lib/types"

// Export report ID utilities
export { ReportIdGenerator } from "./utils/reportIdGenerator"
export { ReportIdMigration } from "./utils/reportIdMigration"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Notification utility functions
export const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'message':
      return MessageSquare;
    case 'alert':
      return AlertTriangle;
    case 'system':
      return Info;
    case 'report_update':
      return Bell;
    default:
      return Bell;
  }
};

export const getPriorityColor = (priority: NotificationPriority): string => {
  switch (priority) {
    case 'urgent':
      return 'text-red-600';
    case 'high':
      return 'text-orange-600';
    case 'medium':
      return 'text-yellow-600';
    case 'low':
      return 'text-green-600';
    default:
      return 'text-gray-600';
  }
};

export const formatTimestamp = (timestamp: string): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  } else {
    return date.toLocaleDateString();
  }
};
