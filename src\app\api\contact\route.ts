import { NextRequest, NextResponse } from 'next/server';
import { emailService } from '@/lib/email/emailService';
import { contactFormSchema } from '@/lib/schemas';

export const runtime = 'nodejs';

// Hardcoded contact data
const HARDCODED_OFFICES = [
  {
    id: 'office-1',
    name: 'Main Office',
    address: '123 Business St, City, State 12345',
    phone: '******-0123',
    email: '<EMAIL>',
    isActive: true
  },
  {
    id: 'office-2', 
    name: 'Regional Office',
    address: '456 Corporate Ave, City, State 67890',
    phone: '******-0456',
    email: '<EMAIL>',
    isActive: true
  }
];

const HARDCODED_DEPARTMENTS = [
  {
    id: 'dept-1',
    name: 'Human Resources',
    email: '<EMAIL>',
    order: 1,
    isActive: true
  },
  {
    id: 'dept-2',
    name: 'Compliance',
    email: '<EMAIL>', 
    order: 2,
    isActive: true
  },
  {
    id: 'dept-3',
    name: 'Legal',
    email: '<EMAIL>',
    order: 3,
    isActive: true
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    
    if (type === 'offices') {
      return NextResponse.json({
        success: true,
        data: HARDCODED_OFFICES
      });
    } else if (type === 'departments') {
      return NextResponse.json({
        success: true,
        data: HARDCODED_DEPARTMENTS
      });
    } else {
      return NextResponse.json({
        success: true,
        data: { 
          offices: HARDCODED_OFFICES, 
          departments: HARDCODED_DEPARTMENTS 
        }
      });
    }
  } catch (error) {
    console.error('Contact API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the form data
    const validationResult = contactFormSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid form data',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const formData = validationResult.data;

    // Check if SMTP is configured
    const smtpConfigured = (process.env.EMAIL_SERVER_HOST || process.env.SMTP_HOST) && 
                          (process.env.EMAIL_SERVER_USER || process.env.SMTP_USER) && 
                          (process.env.EMAIL_SERVER_PASSWORD || process.env.SMTP_PASS);

    if (!smtpConfigured) {
      console.error('SMTP not configured. Missing environment variables.');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is not configured. Please contact the administrator.' 
        },
        { status: 500 }
      );
    }

    // Test SMTP connection first
    const connectionTest = await emailService.testConnection();
    if (!connectionTest) {
      console.error('SMTP connection test failed');
      return NextResponse.json(
        { 
          success: false, 
          error: 'Email service is currently unavailable. Please try again later.' 
        },
        { status: 500 }
      );
    }

    // Send the contact form email to the admin/support team
    // Transform formData to ensure optional fields are properly handled
    const emailFormData = {
      name: formData.name,
      email: formData.email,
      subject: formData.subject,
      message: formData.message,
      ...(formData.phone && { phone: formData.phone }),
      ...(formData.company && { company: formData.company }),
      ...(formData.department && { department: formData.department })
    };
    const emailSent = await emailService.sendContactFormEmail(emailFormData);
    
    if (!emailSent) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to send your message. Please try again later.' 
        },
        { status: 500 }
      );
    }

    // Send auto-reply to the user (optional, don't fail if this fails)
    try {
      await emailService.sendAutoReplyEmail(emailFormData);
    } catch (error) {
      console.warn('Failed to send auto-reply email:', error);
      // Don't fail the main request if auto-reply fails
    }

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon!'
    });

  } catch (error) {
    console.error('Contact form API error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'An unexpected error occurred. Please try again later.' 
      },
      { status: 500 }
    );
  }
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}