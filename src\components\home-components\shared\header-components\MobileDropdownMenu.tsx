"use client";
import Image from "next/image";
import Link from "next/link";
import { memo, useState, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { LOGIN_ITEMS, SIGNUP_ITEMS } from "@/lib/mockData";

interface MobileDropdownMenuProps {
    onItemClick?: () => void;
}

const MobileDropdownMenu = memo(({ onItemClick }: MobileDropdownMenuProps) => {
    const [isLoginOpen, setIsLoginOpen] = useState(false);
    const [isSignupOpen, setIsSignupOpen] = useState(false);

    const toggleLogin = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsLoginOpen(prev => !prev);
    }, []);

    const toggleSignup = useCallback((e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsSignupOpen(prev => !prev);
    }, []);

    return (
        <DropdownMenuContent sideOffset={5} align="end" className="w-[280px] bg-[#ECF4E9] border border-green-200">
            {/* Action Buttons */}
            <div className="space-y-2">
                <DropdownMenuItem asChild>
                    <Link href="/start-trial-free" {...(onItemClick && { onClick: onItemClick })}>
                        <Button className="w-full bg-[#1E4841] text-white hover:bg-green-900 hover:text-gray-100 transition-all duration-300 font-medium justify-center flex items-center gap-2">
                            <Image
                                src="/desktop/home/<USER>/start-trail-free.svg"
                                alt="start trail free icon"
                                height={12}
                                width={17}
                            />
                            Start Trial Free
                        </Button>
                    </Link>
                </DropdownMenuItem>

                {/* Login Dropdown */}
                <div className="py-1 border-y border-[#1E484110]">
                    <Button
                        variant="ghost"
                        onClick={toggleLogin}
                        className="w-full flex justify-between items-center px-3 py-1 text-sm font-semibold text-[#1E4841] opacity-70 hover:bg-green-100 transition-colors duration-300"
                    >
                        <span>Login</span>
                        <ChevronDown className={`transition-transform duration-300 ${isLoginOpen ? 'rotate-180' : ''}`} size={14} />
                    </Button>
                    {isLoginOpen && (
                        <div className="space-y-1 animate-in slide-in-from-top-1 duration-200">
                            {LOGIN_ITEMS.map((item) => (
                                <DropdownMenuItem key={item.href} asChild>
                                    <Link href={item.href} className="flex items-center px-3 py-1 text-sm text-[#1E4841] hover:bg-green-100 rounded-md transition-colors duration-300" {...(onItemClick && { onClick: onItemClick })}>
                                        {item.title}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                        </div>
                    )}
                </div>

                {/* Signup Dropdown */}
                <div className="py-1">
                    <Button
                        onClick={toggleSignup}
                        className="w-full flex justify-between items-center px-3 py-1 text-sm font-semibold text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 transition-colors duration-300"
                    >
                        <span>Signup</span>
                        <ChevronDown className={`transition-transform duration-300 ${isSignupOpen ? 'rotate-180' : ''}`} size={14} />
                    </Button>
                    {isSignupOpen && (
                        <div className="space-y-1 animate-in slide-in-from-top-1 duration-200">
                            {SIGNUP_ITEMS.map((item) => (
                                <DropdownMenuItem key={item.href} asChild>
                                    <Link href={item.href} className="flex items-center px-3 py-1 text-sm text-[#1E4841] hover:bg-green-100 rounded-md transition-colors duration-300" {...(onItemClick && { onClick: onItemClick })}>
                                        {item.title}
                                    </Link>
                                </DropdownMenuItem>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </DropdownMenuContent>
    );
});

MobileDropdownMenu.displayName = 'MobileDropdownMenu';

export default MobileDropdownMenu;