"use client";
import { memo } from "react";
import { NavItem, ProductItem } from "@/lib/types";
import NavCard from "./NavCard";

interface NavContentProps {
    items: NavItem[] | ProductItem[];
    onItemClick?: () => void;
}

const NavContent = memo(({ items, onItemClick }: NavContentProps) => {
    return (
        <div className={`grid grid-cols-1 md:grid-cols-3 gap-4 p-3`}>
            {items.map((item) => (
                <NavCard key={item.href} {...item} {...(onItemClick && { onItemClick })} />
            ))}
        </div>
    );
});

NavContent.displayName = 'NavContent';

export default NavContent;