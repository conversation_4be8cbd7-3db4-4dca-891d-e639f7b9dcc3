"use client";

import React, { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Home,
  User,
  Settings,
  Shield,
  Eye,
  EyeOff,
  Camera,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  Bell,
  Globe,
  Monitor,
  Lock,
  Key,
  RefreshCw,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

// Validation schemas
const profileInfoSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Please enter a valid email address"),
  jobTitle: z.string().optional(),
  department: z.string().optional(),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
});

const passwordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain uppercase, lowercase, number and special character"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const notificationPreferencesSchema = z.object({
  emailNotifications: z.boolean(),
  caseUpdates: z.boolean(),
  securityAlerts: z.boolean(),
  platformUpdates: z.boolean(),
  reminderNotifications: z.boolean(),
});

const privacySettingsSchema = z.object({
  messageHistory: z.string(),
  loginHistory: z.string(),
  attachments: z.string(),
  autoDeleteCases: z.string(),
  anonymousReporting: z.boolean(),
  profileInformationVisibility: z.string(),
  twoWayCommunication: z.boolean(),
  caseReferenceSystem: z.string(),
});

const languageSettingsSchema = z.object({
  language: z.string(),
  timeZone: z.string(),
  dateFormat: z.string(),
  timeFormat: z.string(),
});

type ProfileInfoFormData = z.infer<typeof profileInfoSchema>;
type PasswordFormData = z.infer<typeof passwordSchema>;
type NotificationPreferencesFormData = z.infer<typeof notificationPreferencesSchema>;
type PrivacySettingsFormData = z.infer<typeof privacySettingsSchema>;
type LanguageSettingsFormData = z.infer<typeof languageSettingsSchema>;

export default function ProfileSettingsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("profile");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  // Mock data for active sessions and login history
  const activeSessions = [
    {
      id: "1",
      device: "Chrome Browser",
      location: "San Francisco, CA, USA",
      lastActive: new Date("2025-04-15T09:42:00"),
      current: true,
    },
    {
      id: "2",
      device: "iPhone Pro",
      location: "San Francisco, CA, USA",
      lastActive: new Date("2025-04-14T18:30:00"),
      current: false,
    },
    {
      id: "3",
      device: "Safari",
      location: "San Francisco, CA, USA",
      lastActive: new Date("2025-04-13T14:20:00"),
      current: false,
    },
  ];

  const loginHistory = [
    {
      id: "1",
      timestamp: new Date("2025-04-15T09:42:00"),
      device: "Chrome Browser",
      location: "San Francisco, CA, USA",
      ipAddress: "*************",
      status: "Successful",
    },
    {
      id: "2",
      timestamp: new Date("2025-04-14T18:30:00"),
      device: "iPhone Pro",
      location: "San Francisco, CA, USA",
      ipAddress: "*************",
      status: "Successful",
    },
    {
      id: "3",
      timestamp: new Date("2025-04-13T14:20:00"),
      device: "Safari",
      location: "San Francisco, CA, USA",
      ipAddress: "*************",
      status: "Failed",
    },
    {
      id: "4",
      timestamp: new Date("2025-04-12T10:15:00"),
      device: "Chrome Browser",
      location: "San Francisco, CA, USA",
      ipAddress: "*************",
      status: "Successful",
    },
    {
      id: "5",
      timestamp: new Date("2025-04-11T16:45:00"),
      device: "Firefox Browser",
      location: "San Francisco, CA, USA",
      ipAddress: "*************",
      status: "Successful",
    },
  ];

  const securityLog = [
    {
      id: "1",
      type: "Two-factor authentication enabled",
      timestamp: new Date("2025-04-10T14:30:00"),
      status: "success",
    },
    {
      id: "2",
      type: "Password changed",
      timestamp: new Date("2025-04-08T11:20:00"),
      status: "success",
    },
    {
      id: "3",
      type: "Suspicious login attempt blocked",
      timestamp: new Date("2025-04-05T22:15:00"),
      status: "warning",
    },
    {
      id: "4",
      type: "Email address verified",
      timestamp: new Date("2025-04-01T09:30:00"),
      status: "success",
    },
    {
      id: "5",
      type: "Account created",
      timestamp: new Date("2025-03-30T14:45:00"),
      status: "info",
    },
  ];

  // Form instances
  const profileForm = useForm<ProfileInfoFormData>({
    resolver: zodResolver(profileInfoSchema),
    defaultValues: {
      firstName: user?.firstName || "Emily",
      lastName: user?.lastName || "Johnson",
      email: user?.email || "<EMAIL>",
      jobTitle: "Senior Financial Analyst",
      department: "Finance",
      bio: "Financial professional with 8+ years of experience in corporate accounting and financial analysis.",
    },
  });

  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const notificationForm = useForm<NotificationPreferencesFormData>({
    resolver: zodResolver(notificationPreferencesSchema),
    defaultValues: {
      emailNotifications: true,
      caseUpdates: true,
      securityAlerts: true,
      platformUpdates: false,
      reminderNotifications: true,
    },
  });

  const privacyForm = useForm<PrivacySettingsFormData>({
    resolver: zodResolver(privacySettingsSchema),
    defaultValues: {
      messageHistory: "90",
      loginHistory: "30",
      attachments: "90",
      autoDeleteCases: "Never",
      anonymousReporting: true,
      profileInformationVisibility: "Completely Anonymous",
      twoWayCommunication: true,
      caseReferenceSystem: "Case Number Only",
    },
  });

  const languageForm = useForm<LanguageSettingsFormData>({
    resolver: zodResolver(languageSettingsSchema),
    defaultValues: {
      language: "English (US)",
      timeZone: "(UTC-08:00) Pacific Time (US & Canada)",
      dateFormat: "DD/MM/YYYY",
      timeFormat: "12-hour (AM/PM)",
    },
  });

  // Form submission handlers
  const onProfileSubmit = useCallback(async (_data: ProfileInfoFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Profile information updated successfully");
    } catch {
      toast.error("Failed to update profile information");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onPasswordSubmit = useCallback(async (_data: PasswordFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Password updated successfully");
      passwordForm.reset();
    } catch {
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  }, [passwordForm]);

  const onNotificationSubmit = useCallback(async (_data: NotificationPreferencesFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Notification preferences saved");
    } catch {
      toast.error("Failed to save notification preferences");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onPrivacySubmit = useCallback(async (_data: PrivacySettingsFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Privacy settings saved");
    } catch {
      toast.error("Failed to save privacy settings");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onLanguageSubmit = useCallback(async (_data: LanguageSettingsFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Language and regional settings saved");
    } catch {
      toast.error("Failed to save language settings");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleTerminateSession = useCallback(async (_sessionId: string) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      toast.success("Session terminated successfully");
    } catch {
      toast.error("Failed to terminate session");
    }
  }, []);

  const handleRequestDataExport = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Data export request submitted. You will receive an email when ready.");
    } catch {
      toast.error("Failed to request data export");
    }
  }, []);

  const handleAccountDeletion = useCallback(async () => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Account deletion request submitted");
    } catch {
      toast.error("Failed to request account deletion");
    }
  }, []);

  const handleTwoFactorToggle = useCallback(async (enabled: boolean) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTwoFactorEnabled(enabled);
      toast.success(enabled ? "Two-factor authentication enabled" : "Two-factor authentication disabled");
    } catch {
      toast.error("Failed to update two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <>
      <div className="w-full h-full">
        <Header onNotificationNavigate={() => {}} />
        <main
          id="main-content"
          className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
          aria-label="Profile and Settings"
        >
          {/* Breadcrumb */}
          <section aria-labelledby="breadcrumb-section">
            <h2 id="breadcrumb-section" className="sr-only">Navigation</h2>
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/whistleblower" className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Dashboard
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Profile & Settings</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </section>

          {/* Page Header */}
          <section aria-labelledby="page-header">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 id="page-header" className="text-2xl font-semibold text-[#242E2C]">
                  Profile & Settings
                </h1>
                <p className="text-[#6B7271] mt-1">
                  Manage your account information and preferences
                </p>
              </div>
              <Button
                variant="outline"
                className="w-fit"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </section>

          {/* Settings Tabs */}
          <section aria-labelledby="settings-tabs">
            <h2 id="settings-tabs" className="sr-only">Settings Categories</h2>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 bg-white border border-gray-200 p-1 h-auto">
                <TabsTrigger
                  value="profile"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <User className="w-4 h-4" />
                  <span className="hidden sm:inline">Profile Information</span>
                  <span className="sm:hidden">Profile</span>
                </TabsTrigger>
                <TabsTrigger
                  value="account"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Settings className="w-4 h-4" />
                  <span className="hidden sm:inline">Account Settings</span>
                  <span className="sm:hidden">Account</span>
                </TabsTrigger>
                <TabsTrigger
                  value="privacy"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Eye className="w-4 h-4" />
                  <span className="hidden sm:inline">Privacy Settings</span>
                  <span className="sm:hidden">Privacy</span>
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Shield className="w-4 h-4" />
                  <span className="hidden sm:inline">Security Settings</span>
                  <span className="sm:hidden">Security</span>
                </TabsTrigger>
              </TabsList>

              {/* Profile Information Tab */}
              <TabsContent value="profile" className="space-y-6 mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <User className="w-5 h-5" />
                      Personal Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Profile Picture Section */}
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                      <div className="relative">
                        <Avatar className="w-20 h-20">
                          <AvatarImage src="/dashboard/header/user.svg" alt="Profile picture" />
                          <AvatarFallback className="text-lg">
                            {user?.firstName?.[0]}{user?.lastName?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <Button
                          size="sm"
                          variant="outline"
                          className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                        >
                          <Camera className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-medium text-[#242E2C]">Emily Johnson</h3>
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Active
                          </Badge>
                        </div>
                        <p className="text-sm text-[#6B7271]">
                          Whistleblower ID: WB-7834-2025
                        </p>
                        <p className="text-sm text-[#6B7271]">
                          Member Since: Apr 15, 2025
                        </p>
                        <p className="text-sm text-[#6B7271]">
                          Last Login: Apr 15, 2025 - 09:42 AM
                        </p>
                      </div>
                    </div>

                    <Separator />

                    {/* Profile Form */}
                    <Form {...profileForm}>
                      <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={profileForm.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>First Name</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="Enter your first name" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={profileForm.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Last Name</FormLabel>
                                <FormControl>
                                  <Input {...field} placeholder="Enter your last name" />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={profileForm.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email Address</FormLabel>
                              <FormControl>
                                <Input {...field} type="email" placeholder="Enter your email address" />
                              </FormControl>
                              <p className="text-sm text-[#6B7271]">
                                Your email is used for secure communications and notifications
                              </p>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={profileForm.control}
                          name="jobTitle"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Job Title</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder="Enter your job title" />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={profileForm.control}
                          name="department"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Department</FormLabel>
                              <FormControl>
                                <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select your department" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="Finance">Finance</SelectItem>
                                    <SelectItem value="Human Resources">Human Resources</SelectItem>
                                    <SelectItem value="Operations">Operations</SelectItem>
                                    <SelectItem value="Legal">Legal</SelectItem>
                                    <SelectItem value="IT">Information Technology</SelectItem>
                                    <SelectItem value="Marketing">Marketing</SelectItem>
                                    <SelectItem value="Sales">Sales</SelectItem>
                                    <SelectItem value="Other">Other</SelectItem>
                                  </SelectContent>
                                </Select>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={profileForm.control}
                          name="bio"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Bio</FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder="Tell us about yourself..."
                                  className="min-h-[100px]"
                                />
                              </FormControl>
                              <p className="text-sm text-[#6B7271]">
                                {field.value?.length || 0}/500 characters
                              </p>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex justify-end gap-3">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() => profileForm.reset()}
                          >
                            Cancel
                          </Button>
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Changes"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Account Settings Tab */}
              <TabsContent value="account" className="space-y-6 mt-6">
                {/* Password Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Lock className="w-5 h-5" />
                      Password
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Last changed 21 days ago
                    </p>
                  </CardHeader>
                  <CardContent>
                    <Form {...passwordForm}>
                      <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                        <FormField
                          control={passwordForm.control}
                          name="currentPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Current Password</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    {...field}
                                    type={showCurrentPassword ? "text" : "password"}
                                    placeholder="Enter your current password"
                                  />
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                  >
                                    {showCurrentPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={passwordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>New Password</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    {...field}
                                    type={showNewPassword ? "text" : "password"}
                                    placeholder="Enter your new password"
                                  />
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                    onClick={() => setShowNewPassword(!showNewPassword)}
                                  >
                                    {showNewPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </FormControl>
                              <p className="text-sm text-[#6B7271]">
                                Password must contain uppercase, lowercase, number and special character
                              </p>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={passwordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Confirm New Password</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    {...field}
                                    type={showConfirmPassword ? "text" : "password"}
                                    placeholder="Confirm your new password"
                                  />
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                  >
                                    {showConfirmPassword ? (
                                      <EyeOff className="h-4 w-4" />
                                    ) : (
                                      <Eye className="h-4 w-4" />
                                    )}
                                  </Button>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Updating..." : "Update Password"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Notification Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="w-5 h-5" />
                      Notification Preferences
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...notificationForm}>
                      <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-6">
                        <div className="space-y-4">
                          <FormField
                            control={notificationForm.control}
                            name="emailNotifications"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Email Notifications</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive email notifications for new messages, alerts, and updates
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="caseUpdates"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Case Updates</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive notifications when the status of your cases changes
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="securityAlerts"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Security Alerts</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive notifications about security-related events
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="platformUpdates"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Platform Updates</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive notifications about new features and updates
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="reminderNotifications"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Reminder Notifications</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive reminders about pending actions and deadlines
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Preferences"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Language and Regional Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="w-5 h-5" />
                      Language and Regional Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...languageForm}>
                      <form onSubmit={languageForm.handleSubmit(onLanguageSubmit)} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={languageForm.control}
                            name="language"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Language</FormLabel>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select language" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="English (US)">English (US)</SelectItem>
                                      <SelectItem value="English (UK)">English (UK)</SelectItem>
                                      <SelectItem value="Spanish">Spanish</SelectItem>
                                      <SelectItem value="French">French</SelectItem>
                                      <SelectItem value="German">German</SelectItem>
                                      <SelectItem value="Italian">Italian</SelectItem>
                                      <SelectItem value="Portuguese">Portuguese</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={languageForm.control}
                            name="timeZone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Time Zone</FormLabel>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select time zone" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="(UTC-08:00) Pacific Time (US & Canada)">
                                        (UTC-08:00) Pacific Time (US & Canada)
                                      </SelectItem>
                                      <SelectItem value="(UTC-07:00) Mountain Time (US & Canada)">
                                        (UTC-07:00) Mountain Time (US & Canada)
                                      </SelectItem>
                                      <SelectItem value="(UTC-06:00) Central Time (US & Canada)">
                                        (UTC-06:00) Central Time (US & Canada)
                                      </SelectItem>
                                      <SelectItem value="(UTC-05:00) Eastern Time (US & Canada)">
                                        (UTC-05:00) Eastern Time (US & Canada)
                                      </SelectItem>
                                      <SelectItem value="(UTC+00:00) Greenwich Mean Time">
                                        (UTC+00:00) Greenwich Mean Time
                                      </SelectItem>
                                      <SelectItem value="(UTC+01:00) Central European Time">
                                        (UTC+01:00) Central European Time
                                      </SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={languageForm.control}
                            name="dateFormat"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Date Format</FormLabel>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select date format" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                                      <SelectItem value="DD-MM-YYYY">DD-MM-YYYY</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={languageForm.control}
                            name="timeFormat"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Time Format</FormLabel>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select time format" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="12-hour (AM/PM)">12-hour (AM/PM)</SelectItem>
                                      <SelectItem value="24-hour">24-hour</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Preferences"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Account Deletion */}
                <Card className="border-red-200">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-red-600">
                      <AlertTriangle className="w-5 h-5" />
                      Account Deletion
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Alert className="border-red-200 bg-red-50">
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                      <AlertDescription className="text-red-800">
                        <strong>Warning:</strong> This action cannot be undone.
                        <br />
                        Deleting your account will permanently remove all of your data including your profile information, reports, and messages. Active cases will remain in the system but will be anonymized.
                      </AlertDescription>
                    </Alert>
                    <div className="mt-4">
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button variant="destructive" className="bg-red-600 hover:bg-red-700">
                            Request Account Deletion
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                              This action cannot be undone. This will permanently delete your account and remove your data from our servers.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleAccountDeletion}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Yes, delete my account
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Privacy Settings Tab */}
              <TabsContent value="privacy" className="space-y-6 mt-6">
                {/* Data Retention */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="w-5 h-5" />
                      Data Retention
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Control how long your data is stored in the system. Note that some data may be retained longer for legal compliance purposes.
                    </p>
                  </CardHeader>
                  <CardContent>
                    <Form {...privacyForm}>
                      <form onSubmit={privacyForm.handleSubmit(onPrivacySubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={privacyForm.control}
                            name="messageHistory"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Message History</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  How long to keep your message history
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select retention period" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="30">30 days</SelectItem>
                                      <SelectItem value="90">90 days</SelectItem>
                                      <SelectItem value="180">180 days</SelectItem>
                                      <SelectItem value="365">1 year</SelectItem>
                                      <SelectItem value="never">Never delete</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="loginHistory"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Login History</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  How long to keep your login history
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select retention period" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="30">30 days</SelectItem>
                                      <SelectItem value="90">90 days</SelectItem>
                                      <SelectItem value="180">180 days</SelectItem>
                                      <SelectItem value="365">1 year</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="attachments"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Attachments</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  How long to keep your uploaded attachments
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select retention period" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="30">30 days</SelectItem>
                                      <SelectItem value="90">90 days</SelectItem>
                                      <SelectItem value="180">180 days</SelectItem>
                                      <SelectItem value="365">1 year</SelectItem>
                                      <SelectItem value="never">Never delete</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="autoDeleteCases"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Auto-delete Closed Cases</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  Automatically delete cases after they are closed
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select auto-delete option" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Never">Never</SelectItem>
                                      <SelectItem value="30">30 days after closure</SelectItem>
                                      <SelectItem value="90">90 days after closure</SelectItem>
                                      <SelectItem value="180">180 days after closure</SelectItem>
                                      <SelectItem value="365">1 year after closure</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <Separator />

                        {/* Profile Visibility */}
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium">Profile Visibility</h3>
                          <p className="text-sm text-[#6B7271]">
                            Control who can see your profile information and how you identify yourself in the whistleblowing platform
                          </p>

                          <FormField
                            control={privacyForm.control}
                            name="anonymousReporting"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Anonymous Reporting</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Submit reports without revealing your identity to case handlers
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="profileInformationVisibility"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Profile Information Visibility</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  Control what personal information is visible to case handlers
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select visibility level" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Completely Anonymous">Completely Anonymous</SelectItem>
                                      <SelectItem value="Department Only">Department Only</SelectItem>
                                      <SelectItem value="Name and Department">Name and Department</SelectItem>
                                      <SelectItem value="Full Profile">Full Profile</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="twoWayCommunication"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Two-way Communication</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Allow investigators to contact you about your reports
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={privacyForm.control}
                            name="caseReferenceSystem"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Case Reference in System</FormLabel>
                                <p className="text-sm text-[#6B7271] mb-2">
                                  How you want to reference your cases in the system
                                </p>
                                <FormControl>
                                  <Select {...(field.value && { value: field.value })} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select reference system" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="Case Number Only">Case Number Only</SelectItem>
                                      <SelectItem value="Anonymous ID">Anonymous ID</SelectItem>
                                      <SelectItem value="Department Code">Department Code</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Preferences"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Data Export & Privacy */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Download className="w-5 h-5" />
                      Data Export & Privacy
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <h4 className="font-medium">Export Your Data</h4>
                      <p className="text-sm text-[#6B7271]">
                        You can request a copy of all your personal data stored in the IRIS platform. This export will be provided in a secure, encrypted file.
                      </p>
                      <Button
                        variant="outline"
                        onClick={handleRequestDataExport}
                        className="w-fit"
                      >
                        <Download className="w-4 h-4 mr-2" />
                        Request Data Export
                      </Button>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h4 className="font-medium">Cookie Preferences</h4>
                      <p className="text-sm text-[#6B7271]">
                        Manage how we use cookies to improve your experience on the platform
                      </p>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-medium">Essential Cookies</Label>
                            <p className="text-xs text-[#6B7271]">Required for the platform to function properly. Cannot be disabled.</p>
                          </div>
                          <Switch checked disabled />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-medium">Functional Cookies</Label>
                            <p className="text-xs text-[#6B7271]">Enable tracking and personalization of the platform</p>
                          </div>
                          <Switch defaultChecked />
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <Label className="text-sm font-medium">Analytics Cookies</Label>
                            <p className="text-xs text-[#6B7271]">Help us understand how you use the platform to improve it</p>
                          </div>
                          <Switch />
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        className="w-fit mt-3"
                      >
                        Save Cookie Preferences
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Security Settings Tab */}
              <TabsContent value="security" className="space-y-6 mt-6">
                {/* Two-Factor Authentication */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Two-Factor Authentication
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Add an extra layer of security to your account by requiring a verification code in addition to your password.
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">Text (SMS) Authentication</h4>
                          {twoFactorEnabled && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                              Enabled
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-[#6B7271]">
                          Receive verification codes via SMS to your mobile device
                        </p>
                      </div>
                      <Switch
                        checked={twoFactorEnabled}
                        onCheckedChange={handleTwoFactorToggle}
                        disabled={isLoading}
                      />
                    </div>

                    {twoFactorEnabled && (
                      <Alert className="border-green-200 bg-green-50">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <AlertDescription className="text-green-800">
                          Two-factor authentication is enabled. You will be prompted for a verification code when logging in from a new device.
                        </AlertDescription>
                      </Alert>
                    )}

                    <div className="space-y-2">
                      <h4 className="font-medium">SMS Verification</h4>
                      <p className="text-sm text-[#6B7271]">
                        Your phone number is used to send verification codes. You can update it below.
                      </p>
                      <div className="flex gap-2">
                        <Input
                          placeholder="+****************"
                          value="+****************"
                          disabled
                          className="max-w-xs"
                        />
                        <Button variant="outline" size="sm">
                          Change Number
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <h4 className="font-medium">Backup Codes</h4>
                      <p className="text-sm text-[#6B7271]">
                        Generate backup codes that can be used if you lose access to your phone
                      </p>
                      <Button variant="outline" size="sm">
                        <Key className="w-4 h-4 mr-2" />
                        Generate Backup Codes
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Active Sessions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Monitor className="w-5 h-5" />
                      Active Sessions
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      View and manage devices that are currently logged into your account. You can sign out of any session you don&apos;t recognize.
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {activeSessions.map((session) => (
                        <div
                          key={session.id}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{session.device}</h4>
                              {session.current && (
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                  Current Session
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-[#6B7271]">
                              {session.location}
                            </p>
                            <p className="text-xs text-[#6B7271]">
                              Last active: {format(session.lastActive, "MMM dd, yyyy 'at' h:mm a")}
                            </p>
                          </div>
                          {!session.current && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleTerminateSession(session.id)}
                            >
                              Sign Out
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Login History */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="w-5 h-5" />
                      Login History
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Review recent login attempts to your account. If you see any suspicious activity, please contact support immediately.
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {loginHistory.map((login) => (
                        <div
                          key={login.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-sm">
                                {format(login.timestamp, "MMM dd, yyyy 'at' h:mm a")}
                              </p>
                              <Badge
                                variant={login.status === "Successful" ? "secondary" : "destructive"}
                                className={
                                  login.status === "Successful"
                                    ? "bg-green-100 text-green-800"
                                    : "bg-red-100 text-red-800"
                                }
                              >
                                {login.status}
                              </Badge>
                            </div>
                            <p className="text-sm text-[#6B7271]">
                              {login.device} • {login.location}
                            </p>
                            <p className="text-xs text-[#6B7271]">
                              IP: {login.ipAddress}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-center">
                      <Button variant="outline" size="sm">
                        View Full History
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Security Log */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Security Log
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Key security events related to your account. This helps you monitor any changes to your security settings.
                    </p>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {securityLog.map((event) => (
                        <div
                          key={event.id}
                          className="flex items-start gap-3 p-3 border rounded-lg"
                        >
                          <div className="mt-1">
                            {event.status === "success" && (
                              <CheckCircle className="w-4 h-4 text-green-600" />
                            )}
                            {event.status === "warning" && (
                              <AlertTriangle className="w-4 h-4 text-yellow-600" />
                            )}
                            {event.status === "info" && (
                              <Clock className="w-4 h-4 text-blue-600" />
                            )}
                          </div>
                          <div className="flex-1 space-y-1">
                            <p className="font-medium text-sm">{event.type}</p>
                            <p className="text-xs text-[#6B7271]">
                              {format(event.timestamp, "MMM dd, yyyy 'at' h:mm a")}
                            </p>
                          </div>
                          <Badge
                            variant="secondary"
                            className={
                              event.status === "success"
                                ? "bg-green-100 text-green-800"
                                : event.status === "warning"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-blue-100 text-blue-800"
                            }
                          >
                            {event.status === "success" && "Successful"}
                            {event.status === "warning" && "Warning"}
                            {event.status === "info" && "Info"}
                          </Badge>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-center">
                      <Button variant="outline" size="sm">
                        View Full Security Log
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>
        </main>
      </div>
    </>
  );
}