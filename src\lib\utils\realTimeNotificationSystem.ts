"use client";

import type { Notification } from '@/lib/types';

export interface RealTimeNotification extends Notification {
  isRealTime?: boolean;
  source?: 'websocket' | 'polling' | 'local';
}

export interface NotificationUpdate {
  type: 'new' | 'read' | 'deleted' | 'updated';
  notification: RealTimeNotification;
  timestamp: Date;
}

type NotificationListener = (notifications: RealTimeNotification[]) => void;
type NotificationUpdateListener = (update: NotificationUpdate) => void;

class RealTimeNotificationSystem {
  private static instance: RealTimeNotificationSystem;
  private notifications: RealTimeNotification[] = [];
  private listeners: Set<NotificationListener> = new Set();
  private updateListeners: Set<NotificationUpdateListener> = new Set();
  private wsEventListeners: Map<string, () => void> = new Map();

  private constructor() {
    this.initializeFromStorage();
  }

  public static getInstance(): RealTimeNotificationSystem {
    if (!RealTimeNotificationSystem.instance) {
      RealTimeNotificationSystem.instance = new RealTimeNotificationSystem();
    }
    return RealTimeNotificationSystem.instance;
  }

  /**
   * Initialize WebSocket event listeners
   */
  public initializeWebSocketListeners(wsHook: { addEventListener: (event: string, callback: (event: { data: Record<string, unknown>; timestamp: Date }) => void) => () => void }) {
    // Listen for new notifications
    const unsubscribeNotifications = wsHook.addEventListener('notification', (event: { data: Record<string, unknown>; timestamp: Date }) => {
      const notification = {
        ...event.data,
        isRealTime: true,
        source: 'websocket',
        createdAt: event.data.createdAt ? new Date(event.data.createdAt as string) : event.timestamp
      } as RealTimeNotification;
      
      this.addNotification(notification);
    });

    // Listen for notification updates
    const unsubscribeUpdates = wsHook.addEventListener('notification_update', (event: { data: Record<string, unknown>; timestamp: Date }) => {
      const { notificationId, type, data } = event.data as { notificationId: string; type: string; data?: Record<string, unknown> };
      
      switch (type) {
        case 'read':
          this.markAsRead(notificationId);
          break;
        case 'deleted':
          this.deleteNotification(notificationId);
          break;
        case 'updated':
          if (data) {
            this.updateNotification(notificationId, data);
          }
          break;
      }
    });

    // Store cleanup functions
    this.wsEventListeners.set('notifications', unsubscribeNotifications);
    this.wsEventListeners.set('notification_updates', unsubscribeUpdates);
  }

  /**
   * Clean up WebSocket listeners
   */
  public cleanupWebSocketListeners() {
    this.wsEventListeners.forEach(cleanup => cleanup());
    this.wsEventListeners.clear();
  }

  /**
   * Add a new notification
   */
  public addNotification(notification: RealTimeNotification): void {
    // Check for duplicates
    const exists = this.notifications.some(n => 
      (n._id && n._id === notification._id) || 
      (n.title === notification.title && n.message === notification.message)
    );

    if (!exists) {
      this.notifications.unshift(notification);
      this.persistToStorage();
      this.notifyListeners();
      
      // Notify update listeners
      this.notifyUpdateListeners({
        type: 'new',
        notification,
        timestamp: new Date()
      });

      // Show browser notification if permission granted
      this.showBrowserNotification(notification);
    }
  }

  /**
   * Update notifications (bulk operation)
   */
  public updateNotifications(notifications: RealTimeNotification[]): void {
    // Merge with existing notifications, avoiding duplicates
    const existingIds = new Set(this.notifications.map(n => n._id).filter(Boolean));
    const newNotifications = notifications.filter(n => !existingIds.has(n._id));
    
    this.notifications = [...newNotifications, ...this.notifications];
    this.persistToStorage();
    this.notifyListeners();
  }

  /**
   * Mark notification as read
   */
  public markAsRead(notificationId: string): void {
    const notification = this.notifications.find(n => 
      n._id === notificationId || (n as RealTimeNotification & { id?: string }).id === notificationId
    );
    
    if (notification && notification.status === 'unread') {
      notification.status = 'read';
      notification.readAt = new Date();
      
      this.persistToStorage();
      this.notifyListeners();
      
      this.notifyUpdateListeners({
        type: 'read',
        notification,
        timestamp: new Date()
      });
    }
  }

  /**
   * Mark all notifications as read
   */
  public markAllAsRead(): void {
    let hasChanges = false;
    
    this.notifications.forEach(notification => {
      if (notification.status === 'unread') {
        notification.status = 'read';
        notification.readAt = new Date();
        hasChanges = true;
      }
    });

    if (hasChanges) {
      this.persistToStorage();
      this.notifyListeners();
    }
  }

  /**
   * Delete notification
   */
  public deleteNotification(notificationId: string): void {
    const index = this.notifications.findIndex(n => 
      n._id === notificationId || (n as RealTimeNotification & { id?: string }).id === notificationId
    );
    
    if (index !== -1) {
      const [deletedNotification] = this.notifications.splice(index, 1);
      this.persistToStorage();
      this.notifyListeners();

      if (deletedNotification) {
        this.notifyUpdateListeners({
          type: 'deleted',
          notification: deletedNotification,
          timestamp: new Date()
        });
      }
    }
  }

  /**
   * Update specific notification
   */
  public updateNotification(notificationId: string, updates: Partial<RealTimeNotification>): void {
    const notification = this.notifications.find(n => 
      n._id === notificationId || (n as RealTimeNotification & { id?: string }).id === notificationId
    );
    
    if (notification) {
      Object.assign(notification, updates, { updatedAt: new Date() });
      this.persistToStorage();
      this.notifyListeners();
      
      this.notifyUpdateListeners({
        type: 'updated',
        notification,
        timestamp: new Date()
      });
    }
  }

  /**
   * Get all notifications
   */
  public getNotifications(): RealTimeNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   */
  public getUnreadNotifications(): RealTimeNotification[] {
    return this.notifications.filter(n => n.status === 'unread');
  }

  /**
   * Get unread count
   */
  public getUnreadCount(): number {
    return this.notifications.filter(n => n.status === 'unread').length;
  }

  /**
   * Get notifications by type
   */
  public getNotificationsByType(type: string): RealTimeNotification[] {
    return this.notifications.filter(n => n.type === type);
  }

  /**
   * Get notifications by priority
   */
  public getNotificationsByPriority(priority: string): RealTimeNotification[] {
    return this.notifications.filter(n => n.priority === priority);
  }

  /**
   * Subscribe to notification changes
   */
  public subscribe(listener: NotificationListener): () => void {
    this.listeners.add(listener);
    
    // Immediately call with current notifications
    listener(this.getNotifications());
    
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * Subscribe to notification updates
   */
  public subscribeToUpdates(listener: NotificationUpdateListener): () => void {
    this.updateListeners.add(listener);
    
    return () => {
      this.updateListeners.delete(listener);
    };
  }

  /**
   * Clear all notifications
   */
  public clearAll(): void {
    this.notifications = [];
    this.persistToStorage();
    this.notifyListeners();
  }

  /**
   * Get notification statistics
   */
  public getStatistics() {
    const total = this.notifications.length;
    const unread = this.getUnreadCount();
    const byType = this.notifications.reduce((acc, n) => {
      acc[n.type] = (acc[n.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    const byPriority = this.notifications.reduce((acc, n) => {
      acc[n.priority] = (acc[n.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total,
      unread,
      read: total - unread,
      byType,
      byPriority
    };
  }

  private initializeFromStorage(): void {
    // Only initialize from storage on client side
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      const stored = localStorage.getItem('realtime_notifications');
      if (stored) {
        const parsed = JSON.parse(stored);
        this.notifications = parsed.map((n: Record<string, unknown>) => ({
          ...n,
          createdAt: new Date(n.createdAt as string),
          updatedAt: n.updatedAt ? new Date(n.updatedAt as string) : undefined,
          readAt: n.readAt ? new Date(n.readAt as string) : undefined,
          expiresAt: n.expiresAt ? new Date(n.expiresAt as string) : undefined
        })) as RealTimeNotification[];
      }
    } catch (error) {
      console.error('Error loading notifications from storage:', error);
      this.notifications = [];
    }
  }

  private persistToStorage(): void {
    // Only persist to storage on client side
    if (typeof window === 'undefined') {
      return;
    }
    
    try {
      localStorage.setItem('realtime_notifications', JSON.stringify(this.notifications));
    } catch (error) {
      console.error('Error saving notifications to storage:', error);
    }
  }

  private notifyListeners(): void {
    const notifications = this.getNotifications();
    this.listeners.forEach(listener => {
      try {
        listener(notifications);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  private notifyUpdateListeners(update: NotificationUpdate): void {
    this.updateListeners.forEach(listener => {
      try {
        listener(update);
      } catch (error) {
        console.error('Error in notification update listener:', error);
      }
    });
  }

  private showBrowserNotification(notification: RealTimeNotification): void {
    // Only show browser notifications on client side
    if (typeof window === 'undefined') {
      return;
    }
    
    if ('Notification' in window && Notification.permission === 'granted') {
      try {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          tag: notification._id || `notification-${Date.now()}`,
          requireInteraction: notification.priority === 'urgent'
        });
      } catch (error) {
        console.error('Error showing browser notification:', error);
      }
    }
  }

  /**
   * Request notification permission
   */
  public async requestNotificationPermission(): Promise<NotificationPermission> {
    // Only request permission on client side
    if (typeof window === 'undefined') {
      return 'denied';
    }
    
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission;
    }
    return 'denied';
  }
}

// Export a function to get the instance instead of creating it at module load time
export const getRealTimeNotificationSystem = () => RealTimeNotificationSystem.getInstance();

// Export the instance directly for backward compatibility
export const realTimeNotificationSystem = getRealTimeNotificationSystem();

export type { NotificationListener, NotificationUpdateListener };