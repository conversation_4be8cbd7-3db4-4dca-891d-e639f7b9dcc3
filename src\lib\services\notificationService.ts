import { DataService } from '@/lib/db/dataService';

export class NotificationService {
  /**
   * Create a welcome notification for new users
   */
  static async createWelcomeNotification(userId: string, userRole: string, userName: string, companyName?: string) {
    let message = '';
    let actionUrl = '';

    switch (userRole) {
      case 'admin':
        message = `Welcome ${userName}! As an administrator${companyName ? ` for ${companyName}` : ''}, you can manage reports, assign investigators, and oversee the investigation process. Your dashboard provides comprehensive analytics and case management tools.`;
        actionUrl = '/dashboard/admin';
        break;
      case 'investigator':
        message = `Welcome ${userName}! As an investigator${companyName ? ` for ${companyName}` : ''}, you can review assigned cases, conduct investigations, and communicate securely with whistleblowers. Check your dashboard for new case assignments.`;
        actionUrl = '/dashboard/admin/cases';
        break;
      case 'whistleblower':
        message = `Welcome ${userName}! You can now submit confidential reports, track their progress, and communicate securely with investigators. Your identity and information are protected throughout the process.`;
        actionUrl = '/dashboard/whistleblower';
        break;
      default:
        message = `Welcome ${userName}! Your account has been created successfully.`;
        actionUrl = '/dashboard';
    }

    return await DataService.createNotification({
      userId,
      type: 'system',
      title: 'Welcome to 7IRIS Platform',
      message,
      priority: 'medium',
      actionUrl,
      metadata: {
        source: 'system',
        category: 'welcome',
        userRole
      }
    });
  }

  /**
   * Create notification when a report is submitted
   */
  static async createReportSubmittedNotification(userId: string, reportId: string, reportTitle: string) {
    return await DataService.createNotification({
      userId,
      type: 'report_update',
      title: 'Report Submitted Successfully',
      message: `Your report "${reportTitle}" has been submitted successfully and assigned a unique ID. Our team will review it shortly and you'll be notified of any updates.`,
      priority: 'medium',
      reportId,
      actionUrl: `/dashboard/whistleblower/my-reports`,
      metadata: {
        source: 'system',
        category: 'report_submission',
        reportId
      }
    });
  }

  /**
   * Create notification when a report status changes
   */
  static async createReportStatusUpdateNotification(
    userId: string, 
    reportId: string, 
    reportTitle: string, 
    oldStatus: string, 
    newStatus: string
  ) {
    let message = '';
    let priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium';

    switch (newStatus.toLowerCase()) {
      case 'under review':
        message = `Your report "${reportTitle}" is now under review by our investigation team. You may receive follow-up questions or requests for additional information.`;
        priority = 'medium';
        break;
      case 'in progress':
        message = `Investigation of your report "${reportTitle}" is now in progress. An investigator has been assigned and will contact you if additional information is needed.`;
        priority = 'medium';
        break;
      case 'resolved':
        message = `Great news! Your report "${reportTitle}" has been successfully resolved. Thank you for bringing this matter to our attention.`;
        priority = 'high';
        break;
      case 'closed':
        message = `Your report "${reportTitle}" has been reviewed and closed. If you have any questions about this decision, please contact our support team.`;
        priority = 'medium';
        break;
      default:
        message = `Your report "${reportTitle}" status has been updated from ${oldStatus} to ${newStatus}.`;
        priority = 'low';
    }

    return await DataService.createNotification({
      userId,
      type: 'report_update',
      title: 'Report Status Updated',
      message,
      priority,
      reportId,
      actionUrl: `/dashboard/whistleblower/my-reports`,
      metadata: {
        source: 'system',
        category: 'status_update',
        reportId,
        oldStatus,
        newStatus
      }
    });
  }

  /**
   * Create notification when an investigator is assigned to a report
   */
  static async createInvestigatorAssignedNotification(
    whistleblowerUserId: string,
    investigatorUserId: string,
    reportId: string,
    reportTitle: string,
    investigatorName: string
  ) {
    // Notification for whistleblower
    const whistleblowerNotification = await DataService.createNotification({
      userId: whistleblowerUserId,
      type: 'report_update',
      title: 'Investigator Assigned',
      message: `An investigator (${investigatorName}) has been assigned to your report "${reportTitle}". They may contact you for additional information or clarification.`,
      priority: 'medium',
      reportId,
      actionUrl: `/dashboard/whistleblower/my-reports`,
      metadata: {
        source: 'system',
        category: 'investigator_assignment',
        reportId,
        investigatorId: investigatorUserId
      }
    });

    // Notification for investigator
    const investigatorNotification = await DataService.createNotification({
      userId: investigatorUserId,
      type: 'report_update',
      title: 'New Case Assigned',
      message: `You have been assigned to investigate the report "${reportTitle}". Please review the case details and begin your investigation.`,
      priority: 'high',
      reportId,
      actionUrl: `/dashboard/admin/cases`,
      metadata: {
        source: 'system',
        category: 'case_assignment',
        reportId,
        whistleblowerUserId
      }
    });

    return { whistleblowerNotification, investigatorNotification };
  }

  /**
   * Create notification when a new message is received
   */
  static async createNewMessageNotification(
    recipientUserId: string,
    senderName: string,
    conversationId: string,
    reportTitle?: string,
    messagePreview?: string
  ) {
    const preview = messagePreview && messagePreview.length > 100 ? messagePreview.substring(0, 100) + '...' : messagePreview || '';
    const context = reportTitle ? ` regarding "${reportTitle}"` : '';
    
    return await DataService.createNotification({
      userId: recipientUserId,
      type: 'message',
      title: 'New Message Received',
      message: `${senderName} sent you a message${context}${preview ? `: "${preview}"` : '.'}`,
      priority: 'medium',
      actionUrl: `/dashboard/whistleblower/secure-message?conversation=${conversationId}`,
      metadata: {
        source: 'messaging',
        category: 'new_message',
        conversationId,
        senderName
      }
    });
  }

  /**
   * Create notification for system alerts
   */
  static async createSystemAlertNotification(
    userId: string,
    title: string,
    message: string,
    priority: 'low' | 'medium' | 'high' | 'urgent' = 'medium',
    actionUrl?: string
  ) {
    return await DataService.createNotification({
      userId,
      type: 'alert',
      title,
      message,
      priority,
      actionUrl,
      metadata: {
        source: 'system',
        category: 'alert'
      }
    });
  }

  /**
   * Create notification when a report requires attention
   */
  static async createReportAttentionNotification(
    userId: string,
    reportId: string,
    reportTitle: string,
    reason: string
  ) {
    return await DataService.createNotification({
      userId,
      type: 'report_update',
      title: 'Report Requires Attention',
      message: `Your report "${reportTitle}" requires your attention: ${reason}`,
      priority: 'high',
      reportId,
      actionUrl: `/dashboard/whistleblower/my-reports`,
      metadata: {
        source: 'system',
        category: 'attention_required',
        reportId,
        reason
      }
    });
  }

  /**
   * Create notification for deadline reminders
   */
  static async createDeadlineReminderNotification(
    userId: string,
    reportId: string,
    reportTitle: string,
    deadline: Date
  ) {
    const daysUntilDeadline = Math.ceil((deadline.getTime() - Date.now()) / (1000 * 60 * 60 * 24));
    
    return await DataService.createNotification({
      userId,
      type: 'alert',
      title: 'Deadline Reminder',
      message: `Reminder: Your report "${reportTitle}" has a deadline in ${daysUntilDeadline} day${daysUntilDeadline !== 1 ? 's' : ''}.`,
      priority: daysUntilDeadline <= 1 ? 'urgent' : daysUntilDeadline <= 3 ? 'high' : 'medium',
      reportId,
      actionUrl: `/dashboard/whistleblower/my-reports`,
      metadata: {
        source: 'system',
        category: 'deadline_reminder',
        reportId,
        deadline: deadline.toISOString()
      }
    });
  }

  /**
   * Get user notifications with real-time updates
   */
  static async getUserNotifications(userId: string, limit: number = 10, offset: number = 0) {
    return await DataService.getNotifications(userId, { limit, offset });
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId: string) {
    return await DataService.markNotificationAsRead(notificationId);
  }

  /**
   * Mark all user notifications as read
   */
  static async markAllAsRead(userId: string) {
    return await DataService.markAllNotificationsAsRead(userId);
  }

  /**
   * Get unread notification count
   */
  static async getUnreadCount(userId: string) {
    return await DataService.getUnreadNotificationCount(userId);
  }
}