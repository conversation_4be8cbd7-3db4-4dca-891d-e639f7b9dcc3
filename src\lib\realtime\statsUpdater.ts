import { Server as SocketIOServer } from 'socket.io';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export class RealTimeStatsUpdater {
  private static io: SocketIOServer | null = null;

  static setSocketServer(io: SocketIOServer) {
    this.io = io;
  }

  /**
   * Broadcast updated stats to all connected clients in a company
   */
  static async broadcastStatsUpdate(companyId: string, userId?: string) {
    if (!this.io) return;

    try {
      await connectDB();
      
      // Get updated stats for the company
      const stats = await DataService.getDashboardStats(userId, companyId);
      
      // Broadcast to company room
      this.io.to(`company-${companyId}`).emit('stats-updated', {
        stats,
        timestamp: new Date().toISOString(),
        companyId
      });

      console.log(`📊 Stats broadcasted to company-${companyId}`);
    } catch (error) {
      console.error('Error broadcasting stats update:', error);
    }
  }

  /**
   * Broadcast stats update to specific user
   */
  static async broadcastUserStatsUpdate(userId: string, companyId: string) {
    if (!this.io) return;

    try {
      await connectDB();
      
      // Get user-specific stats
      const stats = await DataService.getDashboardStats(userId, companyId);
      
      // Broadcast to user room
      this.io.to(`user-${userId}`).emit('stats-updated', {
        stats,
        timestamp: new Date().toISOString(),
        userId,
        companyId
      });

      console.log(`📊 User stats broadcasted to user-${userId}`);
    } catch (error) {
      console.error('Error broadcasting user stats update:', error);
    }
  }

  /**
   * Broadcast recent activity update
   */
  static async broadcastRecentActivityUpdate(companyId: string, activity: unknown) {
    if (!this.io) return;

    try {
      // Broadcast new activity to company room
      this.io.to(`company-${companyId}`).emit('recent-activity-updated', {
        activity,
        timestamp: new Date().toISOString(),
        companyId
      });

      console.log(`📈 Recent activity broadcasted to company-${companyId}`);
    } catch (error) {
      console.error('Error broadcasting recent activity update:', error);
    }
  }

  /**
   * Broadcast notification update
   */
  static async broadcastNotificationUpdate(userId: string, notification: unknown) {
    if (!this.io) return;

    try {
      // Broadcast to user room
      this.io.to(`user-${userId}`).emit('notification-received', {
        notification,
        timestamp: new Date().toISOString(),
        userId
      });

      console.log(`🔔 Notification broadcasted to user-${userId}`);
    } catch (error) {
      console.error('Error broadcasting notification update:', error);
    }
  }

  /**
   * Broadcast report status update
   */
  static async broadcastReportStatusUpdate(reportId: string, companyId: string, status: string, userId?: string) {
    if (!this.io) return;

    try {
      const updateData = {
        reportId,
        status,
        timestamp: new Date().toISOString(),
        companyId
      };

      // Broadcast to company room
      this.io.to(`company-${companyId}`).emit('report-status-updated', updateData);

      // If specific user, also broadcast to user room
      if (userId) {
        this.io.to(`user-${userId}`).emit('report-status-updated', updateData);
      }

      // Update stats after report status change
      await this.broadcastStatsUpdate(companyId, userId);

      console.log(`📋 Report status update broadcasted for report-${reportId}`);
    } catch (error) {
      console.error('Error broadcasting report status update:', error);
    }
  }

  /**
   * Broadcast new message in conversation
   */
  static async broadcastNewMessage(conversationId: string, message: unknown, participants: string[]) {
    if (!this.io) return;

    try {
      const messageData = {
        conversationId,
        message,
        timestamp: new Date().toISOString()
      };

      // Broadcast to conversation room
      if (this.io) {
        this.io.to(`conversation-${conversationId}`).emit('new-message', messageData);

        // Also broadcast to each participant's user room
        participants.forEach(participantId => {
          this.io!.to(`user-${participantId}`).emit('new-message', messageData);
        });
      }

      console.log(`💬 New message broadcasted to conversation-${conversationId}`);
    } catch (error) {
      console.error('Error broadcasting new message:', error);
    }
  }
}

/**
 * Trigger stats update after report creation
 */
export async function triggerStatsUpdateOnReportCreate(reportData: unknown) {
  try {
    const typedReportData = reportData as unknown as {
      companyId: string;
      userId: string;
      reportId: string;
      title: string;
      priority: string;
    };
    
    // Update company stats
    await RealTimeStatsUpdater.broadcastStatsUpdate(typedReportData.companyId);
    
    // Update user-specific stats for the whistleblower
    await RealTimeStatsUpdater.broadcastUserStatsUpdate(typedReportData.userId, typedReportData.companyId);
    
    // Broadcast recent activity
    await RealTimeStatsUpdater.broadcastRecentActivityUpdate(typedReportData.companyId, {
      type: 'report_created',
      reportId: typedReportData.reportId,
      title: typedReportData.title,
      priority: typedReportData.priority,
      userId: typedReportData.userId,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error triggering stats update on report create:', error);
  }
}

/**
 * Trigger stats update after message creation
 */
export async function triggerStatsUpdateOnMessage(messageData: unknown, conversationData: unknown) {
  try {
    const typedConversationData = conversationData as unknown as {
      reportId?: {
        companyId?: { toString: () => string };
        reportId?: string;
      };
      _id: string;
    };
    
    const typedMessageData = messageData as unknown as {
      senderId: string;
    };
    
    // Get report data from conversation
    const reportData = typedConversationData.reportId;
    
    if (reportData && reportData.companyId) {
      // Update company stats
      await RealTimeStatsUpdater.broadcastStatsUpdate(reportData.companyId.toString());
      
      // Broadcast recent activity
      await RealTimeStatsUpdater.broadcastRecentActivityUpdate(reportData.companyId.toString(), {
        type: 'message_sent',
        reportId: reportData.reportId || '',
        conversationId: typedConversationData._id,
        senderId: typedMessageData.senderId,
        timestamp: new Date()
      });
    }
  } catch (error) {
    console.error('Error triggering stats update on message:', error);
  }
}

/**
 * Trigger stats update after report status change
 */
export async function triggerStatsUpdateOnStatusChange(reportData: unknown, oldStatus: string, newStatus: string) {
  try {
    const typedReportData = reportData as unknown as {
      companyId: string;
      userId: string;
      _id: string;
      reportId: string;
    };
    
    // Update company stats
    await RealTimeStatsUpdater.broadcastStatsUpdate(typedReportData.companyId);
    
    // Update user-specific stats for the whistleblower
    await RealTimeStatsUpdater.broadcastUserStatsUpdate(typedReportData.userId, typedReportData.companyId);
    
    // Broadcast report status update
    await RealTimeStatsUpdater.broadcastReportStatusUpdate(
      typedReportData._id,
      typedReportData.companyId,
      newStatus,
      typedReportData.userId
    );
    
    // Broadcast recent activity
    await RealTimeStatsUpdater.broadcastRecentActivityUpdate(typedReportData.companyId, {
      type: 'status_changed',
      reportId: typedReportData.reportId,
      oldStatus,
      newStatus,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error triggering stats update on status change:', error);
  }
}

