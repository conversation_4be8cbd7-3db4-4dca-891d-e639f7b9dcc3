import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export async function GET() {
  try {
    await connectDB();
    
    const pricingPlans = await DataService.getPricingPlans();
    
    return NextResponse.json({
      success: true,
      data: pricingPlans
    });
  } catch (error) {
    console.error('Pricing API error:', error);
    
    // Fallback to hardcoded plans if database fails
    const fallbackPlans = [
      {
        _id: 'plan-basic',
        name: 'Basic',
        price: 29,
        features: [
          'Up to 100 reports per month',
          'Basic reporting dashboard',
          'Email notifications',
          'Standard security',
          '24/7 support'
        ],
        order: 1,
        isActive: true
      },
      {
        _id: 'plan-professional',
        name: 'Professional',
        price: 79,
        features: [
          'Up to 500 reports per month',
          'Advanced analytics dashboard',
          'Email & SMS notifications',
          'Enhanced security features',
          'Custom branding',
          'Priority support',
          'API access'
        ],
        order: 2,
        isActive: true
      },
      {
        _id: 'plan-enterprise',
        name: 'Enterprise',
        price: 199,
        features: [
          'Unlimited reports',
          'Full analytics suite',
          'Multi-channel notifications',
          'Advanced security & compliance',
          'Full customization',
          'Dedicated account manager',
          'Full API access',
          'Custom integrations',
          'On-premise deployment option'
        ],
        order: 3,
        isActive: true
      }
    ];
    
    return NextResponse.json({
      success: true,
      data: fallbackPlans,
      fallback: true
    });
  }
}