import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { User, Company } from '@/lib/db/models';
import mongoose from 'mongoose';
import { UserDocument, CompanyDocument } from '@/lib/db/models/interfaces';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    await connectDB();
    
    // Create a default company if it doesn't exist
    let company = await Company.findOne({ name: 'Default Company' });
    
    if (!company) {
      company = await Company.create({
        _id: new mongoose.Types.ObjectId(),
        name: 'Default Company',
        email: '<EMAIL>',
        domain: 'defaultcompany.com',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('Created default company:', company._id);
    }
    
    // Update user with company ID
    const result = await User.findOneAndUpdate(
      { email },
      { companyId: company._id },
      { new: true }
    );
    
    if (!result) {
      return NextResponse.json({
        success: false,
        error: 'User not found'
      });
    }
    
    // Type assertion to access result properties
    const userData = result as unknown as UserDocument;
    const companyData = company as unknown as CompanyDocument;
    
    return NextResponse.json({
      success: true,
      data: {
        email: userData.email,
        companyId: userData.companyId,
        companyName: companyData.name,
        updated: true
      }
    });
  } catch (error) {
    console.error('Fix user company API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}