"use client";

interface ApiRequestOptions extends RequestInit {
  requireAuth?: boolean;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
  }

  private getAuthToken(): string | null {
    if (typeof window === 'undefined') return null;
    const token = localStorage.getItem('auth_token');
    console.log('API Client: Auth token exists:', !!token);
    return token;
  }

  private getHeaders(options: ApiRequestOptions = {}): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add Authorization header if auth is required (default: true)
    const requireAuth = options.requireAuth !== false;
    if (requireAuth) {
      const token = this.getAuthToken();
      if (token) {
        (headers as Record<string, string>)['Authorization'] = `Bearer ${token}`;
      }
    }

    return headers;
  }

  async request<T = unknown>(
    endpoint: string,
    options: ApiRequestOptions = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    const { requireAuth, ...fetchOptions } = options;

    const config: RequestInit = {
      ...fetchOptions,
      headers: this.getHeaders({
        ...(requireAuth !== undefined && { requireAuth }),
        ...(options.headers && { headers: options.headers })
      }),
    };

    console.log(`API Client: Making request to ${url}`, {
      method: config.method || 'GET',
      headers: config.headers,
      requireAuth: requireAuth !== false
    });

    try {
      const response = await fetch(url, config);
      
      console.log(`API Client: Response from ${url}`, {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok
      });
      
      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          console.log('API Client: 401 Unauthorized - clearing token');
          // Token might be expired, clear it
          if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token');
          }
          throw new Error('Authentication failed. Please log in again.');
        }
        
        const errorData = await response.json().catch(() => ({}));
        console.error(`API Client: Error response from ${url}:`, errorData);
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const responseData = await response.json();
      console.log(`API Client: Success response from ${url}:`, responseData);
      return responseData;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Convenience methods
  async get<T = unknown>(endpoint: string, options: ApiRequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = unknown>(endpoint: string, data?: unknown, options: ApiRequestOptions = {}): Promise<T> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'POST',
    };

    if (data !== undefined) {
      requestOptions.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, requestOptions);
  }

  async put<T = unknown>(endpoint: string, data?: unknown, options: ApiRequestOptions = {}): Promise<T> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'PUT',
    };

    if (data !== undefined) {
      requestOptions.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, requestOptions);
  }

  async patch<T = unknown>(endpoint: string, data?: unknown, options: ApiRequestOptions = {}): Promise<T> {
    const requestOptions: ApiRequestOptions = {
      ...options,
      method: 'PATCH',
    };

    if (data !== undefined) {
      requestOptions.body = JSON.stringify(data);
    }

    return this.request<T>(endpoint, requestOptions);
  }

  async delete<T = unknown>(endpoint: string, options: ApiRequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient();

// Export the class for custom instances if needed
export { ApiClient };