import { NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { ReportFilters, ReportStatus, ReportCategory, ReportPriority } from '@/lib/types';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import { NotificationService } from '@/lib/services/notificationService';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      console.error('Reports API: No user in request');
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    console.log('Reports API: User info', {
      userId: request.user.id,
      role: request.user.role,
      companyId: request.user.companyId
    });
        
    const { searchParams } = new URL(request.url);
    const status = searchParams.getAll('status').filter(s => s) as unknown as ReportStatus[];
    const category = searchParams.getAll('category').filter(c => c) as unknown as ReportCategory[];
    const priority = searchParams.getAll('priority').filter(p => p) as unknown as ReportPriority[];
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;
    const offset = searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : undefined;
    
    const filters: ReportFilters = {
      ...(status.length > 0 && { status }),
      ...(category.length > 0 && { category }),
      ...(priority.length > 0 && { priority }),
      ...(limit !== undefined && { limit }),
      ...(offset !== undefined && { offset })
    };
    
    console.log('Reports API: Filters', filters);
    
    // Use company-wise filtering based on user's role and company
    const userId = request.user!.role === 'whistleblower' ? request.user!.id : undefined;
    const companyId = request.user!.companyId;
    
    console.log('Reports API: Query params', { userId, companyId });
    
    const reports = await DataService.getReports(userId, filters, companyId);
    
    console.log('Reports API: Found reports', reports.length);
    
    return NextResponse.json({
      success: true,
      data: reports
    });
  } catch (error) {
    console.error('Reports GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
        
    const reportData = await request.json();
    
    // Ensure the report is created for the authenticated user
    const newReport = await DataService.createReport({
      ...reportData,
      userId: request.user!.id
    });

    // Create notification for report submission
    try {
      await NotificationService.createReportSubmittedNotification(
        request.user!.id,
        newReport._id.toString(),
        reportData.title || 'Untitled Report'
      );
    } catch (notificationError) {
      console.error('Failed to create report submission notification:', notificationError);
      // Don't fail the report creation if notification fails
    }
    
    return NextResponse.json({
      success: true,
      data: newReport
    }, { status: 201 });
  } catch (error) {
    console.error('Reports POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});
