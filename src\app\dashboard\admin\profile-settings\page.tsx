"use client";

import React, { useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import Header from "@/components/dashboard-components/Header";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Home,
  User,
  Settings,
  Shield,
  Eye,
  EyeOff,
  Camera,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  Smartphone,
  Bell,
  Globe,
  Monitor,
  Lock,
  Key,
  RefreshCw,
  Users,
  Activity,
  BarChart3,
  FileText,
  Crown,
  UserCheck,
} from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";

// Admin-specific validation schemas
const adminProfileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.email("Please enter a valid email address"),
  jobTitle: z.string().min(1, "Job title is required"),
  department: z.string().min(1, "Department is required"),
  phoneNumber: z.string().optional(),
  officeLocation: z.string().optional(),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  employeeId: z.string().optional(),
  directReports: z.string().optional(),
});

const adminPasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string()
    .min(12, "Admin password must be at least 12 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      "Password must contain uppercase, lowercase, number and special character"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

const adminNotificationSchema = z.object({
  emailNotifications: z.boolean(),
  caseAssignments: z.boolean(),
  highPriorityCases: z.boolean(),
  systemAlerts: z.boolean(),
  userManagementAlerts: z.boolean(),
  complianceReports: z.boolean(),
  weeklyDigest: z.boolean(),
  monthlyReports: z.boolean(),
  securityAlerts: z.boolean(),
  platformUpdates: z.boolean(),
});

const adminSystemPreferencesSchema = z.object({
  defaultCaseView: z.string(),
  autoAssignCases: z.boolean(),
  showAdvancedFilters: z.boolean(),
  enableBulkActions: z.boolean(),
  defaultReportFormat: z.string(),
  sessionTimeout: z.string(),
  language: z.string(),
  timeZone: z.string(),
  dateFormat: z.string(),
  timeFormat: z.string(),
  theme: z.string(),
});

const adminSecuritySchema = z.object({
  twoFactorAuth: z.boolean(),
  loginNotifications: z.boolean(),
  sessionManagement: z.boolean(),
  ipWhitelist: z.boolean(),
  auditLogging: z.boolean(),
  dataRetention: z.string(),
  backupFrequency: z.string(),
});

type AdminProfileFormData = z.infer<typeof adminProfileSchema>;
type AdminPasswordFormData = z.infer<typeof adminPasswordSchema>;
type AdminNotificationFormData = z.infer<typeof adminNotificationSchema>;
type AdminSystemPreferencesFormData = z.infer<typeof adminSystemPreferencesSchema>;
type AdminSecurityFormData = z.infer<typeof adminSecuritySchema>;

export default function AdminProfileSettingsPage() {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState("profile");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(true);

  // Mock admin data
  const adminStats = {
    totalCases: 247,
    activeCases: 89,
    resolvedCases: 158,
    teamMembers: 12,
    avgResolutionTime: "4.2 days",
    complianceScore: "98%",
  };

  const recentActivity = [
    {
      id: "1",
      type: "case_assigned",
      description: "High priority case assigned to Sarah Chen",
      timestamp: new Date("2025-04-15T10:30:00"),
      severity: "high",
    },
    {
      id: "2",
      type: "user_created",
      description: "New investigator account created for Mike Johnson",
      timestamp: new Date("2025-04-15T09:15:00"),
      severity: "info",
    },
    {
      id: "3",
      type: "system_alert",
      description: "System backup completed successfully",
      timestamp: new Date("2025-04-15T08:00:00"),
      severity: "success",
    },
    {
      id: "4",
      type: "compliance_report",
      description: "Monthly compliance report generated",
      timestamp: new Date("2025-04-14T17:45:00"),
      severity: "info",
    },
  ];

  const teamMembers = [
    {
      id: "1",
      name: "Sarah Chen",
      role: "Senior Investigator",
      email: "<EMAIL>",
      status: "Active",
      lastLogin: new Date("2025-04-15T09:30:00"),
      casesAssigned: 15,
    },
    {
      id: "2",
      name: "Mike Johnson",
      role: "Investigator",
      email: "<EMAIL>",
      status: "Active",
      lastLogin: new Date("2025-04-15T08:45:00"),
      casesAssigned: 12,
    },
    {
      id: "3",
      name: "Lisa Rodriguez",
      role: "Compliance Officer",
      email: "<EMAIL>",
      status: "Active",
      lastLogin: new Date("2025-04-14T16:20:00"),
      casesAssigned: 8,
    },
  ];

  // Form instances
  const profileForm = useForm<AdminProfileFormData>({
    resolver: zodResolver(adminProfileSchema),
    defaultValues: {
      firstName: user?.firstName || "John",
      lastName: user?.lastName || "Smith",
      email: user?.email || "<EMAIL>",
      jobTitle: "Chief Compliance Officer",
      department: "Legal & Compliance",
      phoneNumber: "+****************",
      officeLocation: "New York, NY",
      bio: "Experienced compliance professional with 15+ years in corporate governance and risk management.",
      employeeId: "EMP-2024-001",
      directReports: "12",
    },
  });

  const passwordForm = useForm<AdminPasswordFormData>({
    resolver: zodResolver(adminPasswordSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const notificationForm = useForm<AdminNotificationFormData>({
    resolver: zodResolver(adminNotificationSchema),
    defaultValues: {
      emailNotifications: true,
      caseAssignments: true,
      highPriorityCases: true,
      systemAlerts: true,
      userManagementAlerts: true,
      complianceReports: true,
      weeklyDigest: true,
      monthlyReports: true,
      securityAlerts: true,
      platformUpdates: false,
    },
  });

  const systemPreferencesForm = useForm<AdminSystemPreferencesFormData>({
    resolver: zodResolver(adminSystemPreferencesSchema),
    defaultValues: {
      defaultCaseView: "dashboard",
      autoAssignCases: true,
      showAdvancedFilters: true,
      enableBulkActions: true,
      defaultReportFormat: "pdf",
      sessionTimeout: "30",
      language: "English (US)",
      timeZone: "(UTC-05:00) Eastern Time (US & Canada)",
      dateFormat: "MM/DD/YYYY",
      timeFormat: "12-hour (AM/PM)",
      theme: "light",
    },
  });

  const securityForm = useForm<AdminSecurityFormData>({
    resolver: zodResolver(adminSecuritySchema),
    defaultValues: {
      twoFactorAuth: true,
      loginNotifications: true,
      sessionManagement: true,
      ipWhitelist: false,
      auditLogging: true,
      dataRetention: "7years",
      backupFrequency: "daily",
    },
  });

  // Form submission handlers
  const onProfileSubmit = useCallback(async (_data: AdminProfileFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Admin profile updated successfully");
    } catch {
      toast.error("Failed to update admin profile");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onPasswordSubmit = useCallback(async (_data: AdminPasswordFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Admin password updated successfully");
      passwordForm.reset();
    } catch {
      toast.error("Failed to update password");
    } finally {
      setIsLoading(false);
    }
  }, [passwordForm]);

  const onNotificationSubmit = useCallback(async (_data: AdminNotificationFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Notification preferences saved");
    } catch {
      toast.error("Failed to save notification preferences");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onSystemPreferencesSubmit = useCallback(async (_data: AdminSystemPreferencesFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("System preferences saved");
    } catch {
      toast.error("Failed to save system preferences");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const onSecuritySubmit = useCallback(async (_data: AdminSecurityFormData) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success("Security settings saved");
    } catch {
      toast.error("Failed to save security settings");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleTwoFactorToggle = useCallback(async (enabled: boolean) => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      setTwoFactorEnabled(enabled);
      toast.success(enabled ? "Two-factor authentication enabled" : "Two-factor authentication disabled");
    } catch {
      toast.error("Failed to update two-factor authentication");
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <>
      <div className="w-full h-full">
        <Header onNotificationNavigate={() => {}} />
        <main
          id="main-content"
          className="p-3 sm:p-4 md:p-6 space-y-4 sm:space-y-6 bg-[#F9FAFB] min-h-screen"
          aria-label="Admin Profile and Settings"
        >
          {/* Breadcrumb */}
          <section aria-labelledby="breadcrumb-section">
            <h2 id="breadcrumb-section" className="sr-only">Navigation</h2>
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/dashboard/admin" className="flex items-center gap-2">
                    <Home className="w-4 h-4" />
                    Admin Dashboard
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage>Profile & Settings</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </section>

          {/* Page Header */}
          <section aria-labelledby="page-header">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 id="page-header" className="text-2xl font-semibold text-[#242E2C]">
                  Admin Profile & Settings
                </h1>
                <p className="text-[#6B7271] mt-1">
                  Manage your administrator account and system preferences
                </p>
              </div>
              <div className="flex items-center gap-3">
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                  <Crown className="w-3 h-3 mr-1" />
                  Administrator
                </Badge>
                <Button
                  variant="outline"
                  className="w-fit"
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh
                </Button>
              </div>
            </div>
          </section>

          {/* Admin Stats Overview */}
          <section aria-labelledby="admin-stats">
            <h2 id="admin-stats" className="sr-only">Admin Statistics</h2>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <FileText className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Total Cases</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.totalCases}</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Activity className="w-4 h-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Active Cases</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.activeCases}</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Resolved</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.resolvedCases}</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Users className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Team Members</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.teamMembers}</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <Clock className="w-4 h-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Avg Resolution</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.avgResolutionTime}</p>
                  </div>
                </div>
              </Card>
              <Card className="p-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <BarChart3 className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-[#6B7271]">Compliance</p>
                    <p className="text-lg font-semibold text-[#242E2C]">{adminStats.complianceScore}</p>
                  </div>
                </div>
              </Card>
            </div>
          </section>

          {/* Settings Tabs */}
          <section aria-labelledby="settings-tabs">
            <h2 id="settings-tabs" className="sr-only">Settings Categories</h2>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 bg-white border border-gray-200 p-1 h-auto">
                <TabsTrigger
                  value="profile"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <User className="w-4 h-4" />
                  <span className="hidden sm:inline">Admin Profile</span>
                  <span className="sm:hidden">Profile</span>
                </TabsTrigger>
                <TabsTrigger
                  value="system"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Settings className="w-4 h-4" />
                  <span className="hidden sm:inline">System Preferences</span>
                  <span className="sm:hidden">System</span>
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Bell className="w-4 h-4" />
                  <span className="hidden sm:inline">Notifications</span>
                  <span className="sm:hidden">Alerts</span>
                </TabsTrigger>
                <TabsTrigger
                  value="security"
                  className="flex items-center gap-2 py-3 data-[state=active]:bg-[#1E4841] data-[state=active]:text-white"
                >
                  <Shield className="w-4 h-4" />
                  <span className="hidden sm:inline">Security & Access</span>
                  <span className="sm:hidden">Security</span>
                </TabsTrigger>
              </TabsList>

              {/* Admin Profile Tab */}
              <TabsContent value="profile" className="space-y-6 mt-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Profile Information */}
                  <div className="lg:col-span-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="w-5 h-5" />
                          Administrator Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-6">
                        {/* Profile Picture Section */}
                        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                          <div className="relative">
                            <Avatar className="w-24 h-24">
                              <AvatarImage src="/dashboard/header/user.svg" alt="Admin profile picture" />
                              <AvatarFallback className="text-xl bg-blue-100 text-blue-600">
                                {user?.firstName?.[0]}{user?.lastName?.[0]}
                              </AvatarFallback>
                            </Avatar>
                            <Button
                              size="sm"
                              variant="outline"
                              className="absolute -bottom-2 -right-2 rounded-full w-8 h-8 p-0"
                            >
                              <Camera className="w-4 h-4" />
                            </Button>
                          </div>
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-lg text-[#242E2C]">John Smith</h3>
                              <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                <Crown className="w-3 h-3 mr-1" />
                                Administrator
                              </Badge>
                            </div>
                            <p className="text-sm text-[#6B7271]">
                              Admin ID: ADM-2024-001
                            </p>
                            <p className="text-sm text-[#6B7271]">
                              Member Since: Jan 15, 2024
                            </p>
                            <p className="text-sm text-[#6B7271]">
                              Last Login: Apr 15, 2025 - 09:42 AM
                            </p>
                            <p className="text-sm text-[#6B7271]">
                              Access Level: Full System Administrator
                            </p>
                          </div>
                        </div>

                        <Separator />

                        {/* Profile Form */}
                        <Form {...profileForm}>
                          <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={profileForm.control}
                                name="firstName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>First Name</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter your first name" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={profileForm.control}
                                name="lastName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Last Name</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter your last name" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <FormField
                              control={profileForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Email Address</FormLabel>
                                  <FormControl>
                                    <Input {...field} type="email" placeholder="Enter your email address" />
                                  </FormControl>
                                  <p className="text-sm text-[#6B7271]">
                                    Primary email for system notifications and security alerts
                                  </p>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={profileForm.control}
                                name="jobTitle"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Job Title</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Enter your job title" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={profileForm.control}
                                name="department"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Department</FormLabel>
                                    <FormControl>
                                      <Select value={field.value} onValueChange={field.onChange}>
                                        <SelectTrigger>
                                          <SelectValue placeholder="Select your department" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="Legal & Compliance">Legal & Compliance</SelectItem>
                                          <SelectItem value="Human Resources">Human Resources</SelectItem>
                                          <SelectItem value="Internal Audit">Internal Audit</SelectItem>
                                          <SelectItem value="Risk Management">Risk Management</SelectItem>
                                          <SelectItem value="Executive">Executive</SelectItem>
                                          <SelectItem value="IT Security">IT Security</SelectItem>
                                        </SelectContent>
                                      </Select>
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={profileForm.control}
                                name="phoneNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Phone Number</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="+****************" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={profileForm.control}
                                name="officeLocation"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Office Location</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="City, State" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={profileForm.control}
                                name="employeeId"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Employee ID</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="EMP-2024-001" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={profileForm.control}
                                name="directReports"
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Direct Reports</FormLabel>
                                    <FormControl>
                                      <Input {...field} placeholder="Number of direct reports" />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>

                            <FormField
                              control={profileForm.control}
                              name="bio"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Professional Bio</FormLabel>
                                  <FormControl>
                                    <Textarea
                                      {...field}
                                      placeholder="Brief professional background and expertise..."
                                      className="min-h-[100px]"
                                    />
                                  </FormControl>
                                  <p className="text-sm text-[#6B7271]">
                                    {field.value?.length || 0}/500 characters
                                  </p>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <div className="flex justify-end gap-3">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => profileForm.reset()}
                              >
                                Cancel
                              </Button>
                              <Button
                                type="submit"
                                disabled={isLoading}
                                className="bg-[#1E4841] hover:bg-[#2A5D54]"
                              >
                                {isLoading ? "Saving..." : "Save Changes"}
                              </Button>
                            </div>
                          </form>
                        </Form>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Sidebar - Recent Activity & Team Overview */}
                  <div className="space-y-6">
                    {/* Recent Activity */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-base">
                          <Activity className="w-4 h-4" />
                          Recent Activity
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {recentActivity.map((activity) => (
                            <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-lg">
                              <div className="mt-1">
                                {activity.severity === "high" && (
                                  <AlertTriangle className="w-4 h-4 text-red-600" />
                                )}
                                {activity.severity === "success" && (
                                  <CheckCircle className="w-4 h-4 text-green-600" />
                                )}
                                {activity.severity === "info" && (
                                  <Clock className="w-4 h-4 text-blue-600" />
                                )}
                              </div>
                              <div className="flex-1 space-y-1">
                                <p className="text-sm font-medium">{activity.description}</p>
                                <p className="text-xs text-[#6B7271]">
                                  {format(activity.timestamp, "MMM dd, h:mm a")}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                        <Button variant="outline" size="sm" className="w-full mt-4">
                          View All Activity
                        </Button>
                      </CardContent>
                    </Card>

                    {/* Team Overview */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-base">
                          <Users className="w-4 h-4" />
                          Team Overview
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          {teamMembers.map((member) => (
                            <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                              <div className="flex items-center gap-3">
                                <Avatar className="w-8 h-8">
                                  <AvatarFallback className="text-xs bg-green-100 text-green-600">
                                    {member.name.split(' ').map(n => n[0]).join('')}
                                  </AvatarFallback>
                                </Avatar>
                                <div>
                                  <p className="text-sm font-medium">{member.name}</p>
                                  <p className="text-xs text-[#6B7271]">{member.role}</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-xs text-[#6B7271]">{member.casesAssigned} cases</p>
                                <Badge
                                  variant="secondary"
                                  className="bg-green-100 text-green-800 text-xs"
                                >
                                  {member.status}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                        <Button variant="outline" size="sm" className="w-full mt-4">
                          Manage Team
                        </Button>
                      </CardContent>
                    </Card>

                    {/* Password Change */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2 text-base">
                          <Lock className="w-4 h-4" />
                          Change Password
                        </CardTitle>
                        <p className="text-sm text-[#6B7271]">
                          Last changed 21 days ago
                        </p>
                      </CardHeader>
                      <CardContent>
                        <Form {...passwordForm}>
                          <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-4">
                            <FormField
                              control={passwordForm.control}
                              name="currentPassword"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Current Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input
                                        {...field}
                                        type={showCurrentPassword ? "text" : "password"}
                                        placeholder="Enter current password"
                                      />
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                      >
                                        {showCurrentPassword ? (
                                          <EyeOff className="h-4 w-4" />
                                        ) : (
                                          <Eye className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={passwordForm.control}
                              name="newPassword"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>New Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input
                                        {...field}
                                        type={showNewPassword ? "text" : "password"}
                                        placeholder="Enter new password"
                                      />
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowNewPassword(!showNewPassword)}
                                      >
                                        {showNewPassword ? (
                                          <EyeOff className="h-4 w-4" />
                                        ) : (
                                          <Eye className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </div>
                                  </FormControl>
                                  <p className="text-xs text-[#6B7271]">
                                    Admin password must be at least 12 characters
                                  </p>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={passwordForm.control}
                              name="confirmPassword"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Confirm Password</FormLabel>
                                  <FormControl>
                                    <div className="relative">
                                      <Input
                                        {...field}
                                        type={showConfirmPassword ? "text" : "password"}
                                        placeholder="Confirm new password"
                                      />
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                      >
                                        {showConfirmPassword ? (
                                          <EyeOff className="h-4 w-4" />
                                        ) : (
                                          <Eye className="h-4 w-4" />
                                        )}
                                      </Button>
                                    </div>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <Button
                              type="submit"
                              disabled={isLoading}
                              className="w-full bg-[#1E4841] hover:bg-[#2A5D54]"
                            >
                              {isLoading ? "Updating..." : "Update Password"}
                            </Button>
                          </form>
                        </Form>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              {/* System Preferences Tab */}
              <TabsContent value="system" className="space-y-6 mt-6">
                {/* Dashboard Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Monitor className="w-5 h-5" />
                      Dashboard Preferences
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...systemPreferencesForm}>
                      <form onSubmit={systemPreferencesForm.handleSubmit(onSystemPreferencesSubmit)} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={systemPreferencesForm.control}
                            name="defaultCaseView"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Default Case View</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select default view" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="dashboard">Dashboard Overview</SelectItem>
                                      <SelectItem value="list">List View</SelectItem>
                                      <SelectItem value="kanban">Kanban Board</SelectItem>
                                      <SelectItem value="calendar">Calendar View</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={systemPreferencesForm.control}
                            name="defaultReportFormat"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Default Report Format</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select report format" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="pdf">PDF</SelectItem>
                                      <SelectItem value="excel">Excel</SelectItem>
                                      <SelectItem value="csv">CSV</SelectItem>
                                      <SelectItem value="word">Word Document</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={systemPreferencesForm.control}
                            name="sessionTimeout"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Session Timeout (minutes)</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select timeout" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="15">15 minutes</SelectItem>
                                      <SelectItem value="30">30 minutes</SelectItem>
                                      <SelectItem value="60">1 hour</SelectItem>
                                      <SelectItem value="120">2 hours</SelectItem>
                                      <SelectItem value="240">4 hours</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={systemPreferencesForm.control}
                            name="theme"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Theme</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select theme" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="light">Light</SelectItem>
                                      <SelectItem value="dark">Dark</SelectItem>
                                      <SelectItem value="system">System</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="space-y-4">
                          <FormField
                            control={systemPreferencesForm.control}
                            name="autoAssignCases"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Auto-assign Cases</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Automatically assign new cases to available investigators
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={systemPreferencesForm.control}
                            name="showAdvancedFilters"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Show Advanced Filters</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Display advanced filtering options in case views
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={systemPreferencesForm.control}
                            name="enableBulkActions"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Enable Bulk Actions</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Allow bulk operations on multiple cases simultaneously
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Preferences"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Language and Regional Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="w-5 h-5" />
                      Language and Regional Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={systemPreferencesForm.control}
                        name="language"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Language</FormLabel>
                            <FormControl>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select language" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="English (US)">English (US)</SelectItem>
                                  <SelectItem value="English (UK)">English (UK)</SelectItem>
                                  <SelectItem value="Spanish">Spanish</SelectItem>
                                  <SelectItem value="French">French</SelectItem>
                                  <SelectItem value="German">German</SelectItem>
                                  <SelectItem value="Italian">Italian</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={systemPreferencesForm.control}
                        name="timeZone"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Time Zone</FormLabel>
                            <FormControl>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select time zone" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="(UTC-08:00) Pacific Time (US & Canada)">
                                    (UTC-08:00) Pacific Time (US & Canada)
                                  </SelectItem>
                                  <SelectItem value="(UTC-07:00) Mountain Time (US & Canada)">
                                    (UTC-07:00) Mountain Time (US & Canada)
                                  </SelectItem>
                                  <SelectItem value="(UTC-06:00) Central Time (US & Canada)">
                                    (UTC-06:00) Central Time (US & Canada)
                                  </SelectItem>
                                  <SelectItem value="(UTC-05:00) Eastern Time (US & Canada)">
                                    (UTC-05:00) Eastern Time (US & Canada)
                                  </SelectItem>
                                  <SelectItem value="(UTC+00:00) Greenwich Mean Time">
                                    (UTC+00:00) Greenwich Mean Time
                                  </SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={systemPreferencesForm.control}
                        name="dateFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Date Format</FormLabel>
                            <FormControl>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select date format" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                                  <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                                  <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                                  <SelectItem value="DD-MM-YYYY">DD-MM-YYYY</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={systemPreferencesForm.control}
                        name="timeFormat"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Time Format</FormLabel>
                            <FormControl>
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select time format" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="12-hour (AM/PM)">12-hour (AM/PM)</SelectItem>
                                  <SelectItem value="24-hour">24-hour</SelectItem>
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Notifications Tab */}
              <TabsContent value="notifications" className="space-y-6 mt-6">
                {/* Admin Notification Preferences */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Bell className="w-5 h-5" />
                      Admin Notification Preferences
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Configure how and when you receive notifications about system events and case activities
                    </p>
                  </CardHeader>
                  <CardContent>
                    <Form {...notificationForm}>
                      <form onSubmit={notificationForm.handleSubmit(onNotificationSubmit)} className="space-y-6">
                        <div className="space-y-4">
                          <h4 className="font-medium text-[#242E2C]">Case Management Notifications</h4>

                          <FormField
                            control={notificationForm.control}
                            name="caseAssignments"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Case Assignments</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Notify when cases are assigned to team members
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="highPriorityCases"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">High Priority Cases</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Immediate alerts for high priority or urgent cases
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="userManagementAlerts"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">User Management Alerts</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Notifications about user account changes and access requests
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <Separator />

                        <div className="space-y-4">
                          <h4 className="font-medium text-[#242E2C]">System & Security Notifications</h4>

                          <FormField
                            control={notificationForm.control}
                            name="systemAlerts"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">System Alerts</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Critical system events, maintenance, and downtime notifications
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="securityAlerts"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Security Alerts</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Security incidents, failed login attempts, and access violations
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="platformUpdates"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Platform Updates</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    New features, system updates, and maintenance schedules
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <Separator />

                        <div className="space-y-4">
                          <h4 className="font-medium text-[#242E2C]">Reporting & Compliance</h4>

                          <FormField
                            control={notificationForm.control}
                            name="complianceReports"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Compliance Reports</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Automated compliance reports and regulatory updates
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="weeklyDigest"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Weekly Digest</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Weekly summary of case activities and team performance
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="monthlyReports"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Monthly Reports</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Comprehensive monthly analytics and compliance reports
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={notificationForm.control}
                            name="emailNotifications"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Email Notifications</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Master toggle for all email notifications
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Preferences"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Security & Access Tab */}
              <TabsContent value="security" className="space-y-6 mt-6">
                {/* Two-Factor Authentication */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Shield className="w-5 h-5" />
                      Two-Factor Authentication
                    </CardTitle>
                    <p className="text-sm text-[#6B7271]">
                      Enhanced security for administrator accounts with mandatory 2FA
                    </p>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50 border-green-200">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">SMS Authentication</h4>
                          <Badge variant="secondary" className="bg-green-100 text-green-800">
                            Enabled
                          </Badge>
                        </div>
                        <p className="text-sm text-[#6B7271]">
                          Verification codes sent to +1 (555) ***-4567
                        </p>
                      </div>
                      <Switch
                        checked={twoFactorEnabled}
                        onCheckedChange={handleTwoFactorToggle}
                        disabled={isLoading}
                      />
                    </div>

                    <Alert className="border-blue-200 bg-blue-50">
                      <CheckCircle className="h-4 w-4 text-blue-600" />
                      <AlertDescription className="text-blue-800">
                        <strong>Admin Security Policy:</strong> Two-factor authentication is required for all administrator accounts and cannot be disabled.
                      </AlertDescription>
                    </Alert>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">Backup Codes</h4>
                        <p className="text-sm text-[#6B7271]">
                          Emergency access codes for account recovery
                        </p>
                        <Button variant="outline" size="sm">
                          <Key className="w-4 h-4 mr-2" />
                          Generate New Codes
                        </Button>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">Authenticator App</h4>
                        <p className="text-sm text-[#6B7271]">
                          Use an authenticator app for enhanced security
                        </p>
                        <Button variant="outline" size="sm">
                          <Smartphone className="w-4 h-4 mr-2" />
                          Setup Authenticator
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Security Settings */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Lock className="w-5 h-5" />
                      Security Settings
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Form {...securityForm}>
                      <form onSubmit={securityForm.handleSubmit(onSecuritySubmit)} className="space-y-6">
                        <div className="space-y-4">
                          <FormField
                            control={securityForm.control}
                            name="loginNotifications"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Login Notifications</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Receive alerts for all login attempts to your admin account
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={securityForm.control}
                            name="sessionManagement"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Advanced Session Management</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Enhanced session tracking and automatic logout on suspicious activity
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={securityForm.control}
                            name="ipWhitelist"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">IP Address Whitelist</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Restrict admin access to specific IP addresses or ranges
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={securityForm.control}
                            name="auditLogging"
                            render={({ field }) => (
                              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                                <div className="space-y-0.5">
                                  <FormLabel className="text-base">Comprehensive Audit Logging</FormLabel>
                                  <p className="text-sm text-[#6B7271]">
                                    Log all administrative actions for compliance and security review
                                  </p>
                                </div>
                                <FormControl>
                                  <Switch
                                    checked={field.value}
                                    onCheckedChange={field.onChange}
                                  />
                                </FormControl>
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={securityForm.control}
                            name="dataRetention"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Data Retention Policy</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select retention period" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="1year">1 Year</SelectItem>
                                      <SelectItem value="3years">3 Years</SelectItem>
                                      <SelectItem value="5years">5 Years</SelectItem>
                                      <SelectItem value="7years">7 Years</SelectItem>
                                      <SelectItem value="10years">10 Years</SelectItem>
                                      <SelectItem value="indefinite">Indefinite</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <p className="text-sm text-[#6B7271]">
                                  How long to retain case data and audit logs
                                </p>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={securityForm.control}
                            name="backupFrequency"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Backup Frequency</FormLabel>
                                <FormControl>
                                  <Select value={field.value} onValueChange={field.onChange}>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select backup frequency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="hourly">Hourly</SelectItem>
                                      <SelectItem value="daily">Daily</SelectItem>
                                      <SelectItem value="weekly">Weekly</SelectItem>
                                      <SelectItem value="monthly">Monthly</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </FormControl>
                                <p className="text-sm text-[#6B7271]">
                                  Automated system backup schedule
                                </p>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="flex justify-end">
                          <Button
                            type="submit"
                            disabled={isLoading}
                            className="bg-[#1E4841] hover:bg-[#2A5D54]"
                          >
                            {isLoading ? "Saving..." : "Save Security Settings"}
                          </Button>
                        </div>
                      </form>
                    </Form>
                  </CardContent>
                </Card>

                {/* Access Management */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <UserCheck className="w-5 h-5" />
                      Access Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">Admin Permissions</h4>
                        <div className="space-y-2">
                          <div className="flex items-center justify-between text-sm">
                            <span>User Management</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">Full Access</Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Case Management</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">Full Access</Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>System Configuration</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">Full Access</Badge>
                          </div>
                          <div className="flex items-center justify-between text-sm">
                            <span>Audit & Compliance</span>
                            <Badge variant="secondary" className="bg-green-100 text-green-800">Full Access</Badge>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <h4 className="font-medium">Recent Access</h4>
                        <div className="space-y-2 text-sm text-[#6B7271]">
                          <div>Last login: Apr 15, 2025 - 09:42 AM</div>
                          <div>Previous login: Apr 14, 2025 - 17:30 PM</div>
                          <div>Failed attempts: 0 (last 30 days)</div>
                          <div>Active sessions: 1</div>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                      <h4 className="font-medium">Emergency Access</h4>
                      <p className="text-sm text-[#6B7271]">
                        In case of emergency, contact the system administrator or use the emergency access procedures outlined in the security policy.
                      </p>
                      <Button variant="outline" size="sm">
                        <Download className="w-4 h-4 mr-2" />
                        Download Security Policy
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>
        </main>
      </div>
    </>
  );
}