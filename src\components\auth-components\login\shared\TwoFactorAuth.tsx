"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { useEffect, useRef, useState } from "react";

interface TwoFactorAuthProps {
    isOpen: boolean;
    onClose: () => void;
    verificationCode: string[];
    onVerificationCodeChange: (index: number, value: string) => void;
    onVerify: () => void;
    isLoading: boolean;
    onResendCode?: () => void;
    expiryTime?: Date;
    userId?: string;
}

export default function TwoFactorAuth({
    isOpen,
    onClose,
    verificationCode,
    onVerificationCodeChange,
    onVerify,
    isLoading
}: TwoFactorAuthProps) {
    const [timeLeft, setTimeLeft] = useState(600); // Default 10 minutes in seconds
    const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

    // Initialize timer based on expiry time
    useEffect(() => {
        setTimeLeft(600); // Default 10 minutes
    }, []);

    // Timer countdown effect
    useEffect(() => {
        if (!isOpen) return;
        
        const interval = setInterval(() => {
            setTimeLeft(prevTime => {
                const newTime = Math.max(0, prevTime - 1);
                return newTime;
            });
        }, 1000);
        
        return () => clearInterval(interval);
    }, [isOpen]);

    // Auto-focus on first input when dialog opens
    useEffect(() => {
        if (isOpen) {
            const timer = setTimeout(() => {
                const firstInput = inputRefs.current[0];
                if (firstInput && document.contains(firstInput)) {
                    firstInput.focus();
                }
            }, 100);
            
            return () => clearTimeout(timer);
        }
    }, [isOpen]);

    // Handle input focus and auto-advance
    const handleInput = (index: number, value: string) => {
        onVerificationCodeChange(index, value);
        
        // Auto-advance to next input
        if (value && index < 5) {
            const nextInput = inputRefs.current[index + 1];
            if (nextInput && document.contains(nextInput)) {
                nextInput.focus();
            }
        }
    };

    // Handle backspace to go to previous input
    const handleKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.key === 'Backspace' && !verificationCode[index] && index > 0) {
            const prevInput = inputRefs.current[index - 1];
            if (prevInput && document.contains(prevInput)) {
                prevInput.focus();
            }
        }
    };

    const handleResendCode = async () => {
        // Reset timer and request new code
        setTimeLeft(600);
        // Implementation for resending code would go here
        console.log('Resending verification code...');
    };

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${String(mins).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Two Factor Authentication</DialogTitle>
                    <DialogDescription>
                        Enter the 6-digit verification code sent to your registered device or email.
                    </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4">
                    <div className="flex justify-between gap-2">
                        {verificationCode.map((digit, index) => (
                            <Input
                                key={index}
                                id={`code-${index}`}
                                type="text"
                                inputMode="numeric"
                                pattern="[0-9]*"
                                maxLength={1}
                                value={digit}
                                onChange={(e) => handleInput(index, e.target.value)}
                                onKeyDown={(e) => handleKeyDown(index, e)}
                                className="w-12 h-12 text-center text-2xl"
                                ref={el => { inputRefs.current[index] = el; }}
                                autoComplete="one-time-code"
                            />
                        ))}
                    </div>
                    <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">
                            Code expires in: {formatTime(timeLeft)}
                        </span>
                        <Button 
                            variant="link" 
                            className="text-sm"
                            onClick={handleResendCode}
                            disabled={timeLeft > 540} // Disable for first 60 seconds
                        >
                            Resend Code
                        </Button>
                    </div>
                    <Button
                        onClick={onVerify}
                        disabled={isLoading || verificationCode.join('').length !== 6}
                    >
                        {isLoading ? "Verifying..." : "Verify & Continue"}
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
}
