import { LayoutDashboard, MenuSquare, MessageSquareMore, Settings, HelpCircleIcon, Users, Bell, FileText, AlertTriangle, BarChart3 } from "lucide-react";

export const PROFILE_ITEMS = [
    { href: "/dashboard/whistleblower/profile-settings", title: "Change Profile" },
    { href: "/dashboard/whistleblower/profile-settings", title: "Settings" },
    { href: "/logout", title: "Logout" }
];

export const NAVIGATION_ITEMS = [
    {
        title: "Dashboard",
        url: "/dashboard/whistleblower",
        icon: LayoutDashboard,
    },
    {
        title: "My Reports",
        url: "/dashboard/whistleblower/my-reports",
        icon: MenuSquare,
    },
    {
        title: "Secure Messages",
        url: "/dashboard/whistleblower/secure-message",
        icon: MessageSquareMore,
    },
    {
        title: "Profile & Settings",
        url: "/dashboard/whistleblower/profile-settings",
        icon: Setting<PERSON>,
    },
    {
        title: "Help / FAQ",
        url: "/dashboard/whistleblower/help",
        icon: HelpCircleIcon,
    },
];

export const ADMIN_NAVIGATION_ITEMS = [
    {
        title: "Main",
        items: [
            {
                title: "Dashboard",
                url: "/dashboard/admin",
                icon: LayoutDashboard,
            },
            {
                title: "Case Management",
                url: "/dashboard/admin/case-management",
                icon: MenuSquare,
            },
            {
                title: "User Management",
                url: "/dashboard/admin/user-management",
                icon: Users,
            },
            {
                title: "Notifications",
                url: "/dashboard/admin/notifications",
                icon: Bell,
            },
            {
                title: "Communications",
                url: "/dashboard/admin/secure-message",
                icon: MessageSquareMore,
            },
        ]
    },
    {
        title: "Management",
        items: [
            {
                title: "Escalations & SLAs",
                url: "/dashboard/admin/escalations",
                icon: AlertTriangle,
            },
            {
                title: "Audit Logs",
                url: "/dashboard/admin/audit-logs",
                icon: FileText,
            },
            {
                title: "Reports & Analytics",
                url: "/dashboard/admin/reports-analytics",
                icon: BarChart3,
            },
        ]
    },
    {
        title: "System",
        items: [
            {
                title: "Settings",
                url: "/dashboard/admin/profile-settings",
                icon: Settings,
            },
            {
                title: "Help & Support",
                url: "/dashboard/admin/help",
                icon: HelpCircleIcon,
            },

        ]
    }
];

export const languageOptions = [
    { code: 'en', name: 'English' },
    { code: 'es', name: 'Español' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' }
];