import { Schema } from 'mongoose';
import { createModel } from '../utils';

const EvidenceFileSchema = new Schema({
  fileName: { type: String, required: true },
  originalName: { type: String, required: true },
  fileSize: { type: Number, required: true },
  mimeType: { type: String, required: true },
  fileUrl: { type: String, required: true },
  uploadedAt: { type: Date, default: Date.now },
  uploadedBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  description: { type: String },
  isEncrypted: { type: Boolean, default: false }
});

const ReportSchema = new Schema({
  reportId: { type: String, required: true, unique: true },
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  companyId: { type: Schema.Types.ObjectId, ref: 'Company' },
  title: { type: String, required: true },
  description: { type: String, required: true },
  category: { 
    type: String, 
    enum: ['Fraud', 'Corruption', 'Harassment', 'Safety Violation', 'Environmental', 'Discrimination', 'Other'],
    required: true 
  },
  priority: { 
    type: String, 
    enum: ['Low', 'Medium', 'High', 'Critical'],
    required: true 
  },
  status: { 
    type: String, 
    enum: ['Draft', 'New', 'Under Review', 'Awaiting Response', 'Resolved', 'Closed'],
    default: 'New'
  },
  isDraft: { type: Boolean, default: false },
  isAnonymous: { type: Boolean, default: false },
  incidentDate: { type: Date },
  location: { type: String },
  evidence: [EvidenceFileSchema],
  assignedInvestigator: { type: Schema.Types.ObjectId, ref: 'User' },
  progress: { type: Number, default: 0, min: 0, max: 100 },
  estimatedCompletion: { type: Date },
  tags: [{ type: String }],
  metadata: {
    ipAddress: { type: String },
    userAgent: { type: String },
    submissionMethod: { 
      type: String, 
      enum: ['web', 'mobile', 'api'],
      default: 'web'
    }
  }
}, {
  timestamps: true
});

// Use the utility function to create the model safely
const Report = createModel('Report', ReportSchema);

export default Report;