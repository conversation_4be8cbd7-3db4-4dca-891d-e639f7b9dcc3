"use client";

import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { apiClient } from '@/lib/api/client';

interface MessageIndicatorData {
    hasUnread: boolean;
    count: number;
    formattedCount: string;
}

export function useMessageIndicators(): MessageIndicatorData {
    const { user } = useAuth();
    const [unreadCount, setUnreadCount] = useState(0);

    useEffect(() => {
        const fetchUnreadCount = async () => {
            if (!user?.id) {
                setUnreadCount(0);
                return;
            }

            try {
                // First, let's check if we have any conversations at all
                const conversationsData = await apiClient.get<{ success: boolean; data: Array<{ id: string; unreadCount?: number }> }>(`/api/conversations?userId=${user.id}`);
                
                if (conversationsData.success) {
                    // For now, since we don't have proper unread message tracking,
                    // let's default to 0 unread messages to prevent false counts
                    // TODO: Implement proper message read/unread status tracking
                    setUnreadCount(0);
                } else {
                    setUnreadCount(0);
                }
            } catch (error) {
                console.error('Error fetching unread message count:', error);
                setUnreadCount(0);
            }
        };

        fetchUnreadCount();
        
        // Refresh count every 30 seconds
        const interval = setInterval(fetchUnreadCount, 30000);
        
        return () => clearInterval(interval);
    }, [user?.id]);

    const formatUnreadCount = (count: number): string => {
        if (count === 0) return "";
        if (count > 9) return "9+";
        return count.toString();
    };

    return {
        hasUnread: unreadCount > 0,
        count: unreadCount,
        formattedCount: formatUnreadCount(unreadCount)
    };
}