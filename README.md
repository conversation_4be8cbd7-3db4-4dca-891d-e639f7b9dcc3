# 🔒 Whistleblower Platform

A comprehensive, secure whistleblower reporting platform built with Next.js 15, featuring real-time messaging, advanced security, and multi-role dashboards.

## 🌟 Features

### 🛡️ Security & Privacy
- **End-to-end encryption** for sensitive communications
- **Anonymous reporting** with optional identity disclosure
- **JWT-based authentication** with 2FA support
- **Role-based access control** (RBAC)
- **Account lockout protection** against brute force attacks
- **Secure file uploads** with virus scanning

### 💬 Real-time Communication
- **Socket.IO integration** for instant messaging
- **Typing indicators** and read receipts
- **File attachments** with secure storage
- **Conversation management** between whistleblowers and investigators
- **Real-time notifications** and status updates

### 📊 Advanced Dashboards
- **Multi-role interfaces**: Whistleblower, Investigator, Admin
- **Interactive analytics** with Recharts
- **Report tracking** with progress indicators
- **Case management** with assignment workflows
- **Audit logs** and compliance reporting

### 🎨 Modern UI/UX
- **Responsive design** with Tailwind CSS
- **Dark/Light theme** support
- **Accessible components** with Radix UI
- **Rich text editor** with Lexical
- **Toast notifications** with Sonner

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Backend**: Node.js, Express, Socket.IO
- **Database**: MongoDB with Mongoose ODM
- **Authentication**: NextAuth.js with JWT
- **Styling**: Tailwind CSS, Radix UI
- **Real-time**: Socket.IO
- **Forms**: React Hook Form with Zod validation

### Project Structure
```
src/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── (home)/            # Public pages
│   ├── dashboard/         # Protected dashboards
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── auth-components/  # Authentication components
│   ├── dashboard-components/ # Dashboard-specific components
│   └── home-components/  # Public page components
├── lib/                  # Core utilities
│   ├── db/              # Database models & services
│   ├── auth/            # Authentication logic
│   ├── socket/          # Socket.IO server
│   ├── utils/           # Helper functions
│   └── types.ts         # TypeScript definitions
├── hooks/               # Custom React hooks
└── providers/           # Context providers
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- MongoDB 6+
- npm/yarn/pnpm

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd whistleblower-new
```

2. **Install dependencies**
```bash
npm install
# or
pnpm install
```

3. **Environment setup**
```bash
cp .env.example .env.local
```

Configure your environment variables:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/whistleblower
MONGODB_DB=whistleblower

# Authentication
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3002
JWT_SECRET=your-jwt-secret

# Socket.IO
WS_PORT=3002

# Email (optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

4. **Database setup**
```bash
# Seed the database with initial data
npm run db:seed

# Verify database connection
npm run db:verify
```

5. **Start development server**
```bash
npm run dev
```

The application will be available at `http://localhost:3002`

## 📋 Available Scripts

### Development
- `npm run dev` - Start development server with Socket.IO
- `npm run dev:next` - Start Next.js only (no Socket.IO)
- `npm run build` - Build for production
- `npm run start` - Start production server

### Database
- `npm run db:seed` - Seed database with sample data
- `npm run db:verify` - Verify database connection
- `npm run db:clean` - Clean database (removes all data)

### Code Quality
- `npm run lint` - Run ESLint
- `npm run build:no-lint` - Build without linting

## 🔐 Authentication & Authorization

### User Roles
- **Whistleblower**: Submit reports, track progress, communicate with investigators
- **Investigator**: Review reports, communicate with whistleblowers, update case status
- **Admin**: Manage users, view analytics, system configuration

### Security Features
- Password hashing with bcrypt
- JWT tokens with refresh mechanism
- Two-factor authentication (2FA)
- Account lockout after failed attempts
- Session management with automatic expiry

## 📊 Database Schema

### Core Models
- **User**: Authentication and profile data
- **Report**: Whistleblower submissions with evidence
- **Conversation**: Secure messaging between parties
- **Message**: Individual messages with encryption
- **Notification**: System and user notifications
- **Company**: Multi-tenant organization support

### Key Relationships
```
User ←→ Report (1:many)
Report ←→ Conversation (1:1)
Conversation ←→ Message (1:many)
User ←→ Notification (1:many)
Company ←→ User (1:many)
```

## 🔌 Real-time Features

### Socket.IO Implementation
- **Authentication middleware** for secure connections
- **Room-based messaging** for private conversations
- **Typing indicators** and presence status
- **Real-time notifications** for status updates
- **Connection monitoring** with automatic reconnection

### Event Types
- `connection` - User connects to socket
- `authenticate` - User authentication
- `join_conversation` - Join private room
- `send_message` - Send encrypted message
- `typing_start/stop` - Typing indicators
- `notification` - Real-time notifications

## 🎨 UI Components

### Design System
- **Consistent theming** with CSS variables
- **Accessible components** following WCAG guidelines
- **Responsive breakpoints** for all screen sizes
- **Animation system** with Tailwind CSS

### Key Components
- **DataTable**: Advanced table with sorting, filtering, pagination
- **RichTextEditor**: Lexical-based editor for reports
- **FileUpload**: Secure file handling with progress
- **NotificationSystem**: Toast and in-app notifications
- **ChatInterface**: Real-time messaging UI

## 🔧 Configuration

### Environment Variables
```env
# Required
MONGODB_URI=mongodb://localhost:27017/whistleblower
NEXTAUTH_SECRET=your-secret-key
JWT_SECRET=your-jwt-secret

# Optional
WS_PORT=3002
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
UPLOAD_MAX_SIZE=10485760
ENCRYPTION_KEY=your-encryption-key
```

### Feature Flags
Configure features in `src/lib/constants.ts`:
```typescript
export const FEATURES = {
  ANONYMOUS_REPORTS: true,
  TWO_FACTOR_AUTH: true,
  FILE_UPLOADS: true,
  REAL_TIME_CHAT: true,
  EMAIL_NOTIFICATIONS: true
};
```

## 📈 Performance

### Optimizations
- **Next.js 15** with Turbopack for fast builds
- **React 19** with concurrent features
- **Image optimization** with Next.js Image
- **Code splitting** with dynamic imports
- **Database indexing** for query performance

### Monitoring
- **Real-time connection monitoring**
- **Database query optimization**
- **Error tracking and logging**
- **Performance metrics collection**

## 🧪 Testing

### Test Structure
```bash
# Unit tests
npm run test

# Integration tests
npm run test:integration

# E2E tests
npm run test:e2e
```

### Testing Stack
- **Jest** for unit testing
- **React Testing Library** for component tests
- **Playwright** for E2E testing
- **MSW** for API mocking

## 🚀 Deployment

### Production Build
```bash
npm run build
npm run start
```

### Docker Deployment
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3002
CMD ["npm", "start"]
```

### Environment Setup
- Configure production MongoDB cluster
- Set up SSL certificates
- Configure reverse proxy (Nginx/Apache)
- Set up monitoring and logging

## 🤝 Contributing

### Development Workflow
1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Code Standards
- **TypeScript** for type safety
- **ESLint** for code quality
- **Prettier** for formatting
- **Conventional Commits** for commit messages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Documentation
- [API Documentation](docs/api.md)
- [Component Library](docs/components.md)
- [Deployment Guide](docs/deployment.md)

### Getting Help
- Create an issue for bugs
- Start a discussion for questions
- Check existing documentation

## 🔄 Changelog

### v0.1.0 (Current)
- ✅ Initial release with core features
- ✅ Multi-role authentication system
- ✅ Real-time messaging with Socket.IO
- ✅ Secure report submission
- ✅ Advanced dashboard analytics
- ✅ Mobile-responsive design

### Roadmap
- 🔄 Mobile app (React Native)
- 🔄 Advanced analytics dashboard
- 🔄 Multi-language support
- 🔄 API rate limiting
- 🔄 Advanced file processing
- 🔄 Integration with external systems

---

**Built with ❤️ for secure, anonymous reporting**