"use client";

import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './useAuth';
import { apiClient } from '@/lib/api/client';
import {
  ReportData,
  RecentMessage,
  ActivityItem,
  Notification
} from '@/lib/types';
import { ReportDocument } from '@/lib/db/models/interfaces';
import {
  transformReportData,
  transformConversationToMessage,
  transformActivityData
} from '@/lib/utils/dataTransformers';

// Import the ConversationApi and ActivityApi types from dataTransformers
interface ConversationApi {
  participants?: Array<{
    _id: string;
    role: string;
    firstName?: string;
    lastName?: string;
  }>;
  lastMessage?: { content?: string };
  lastMessageAt?: string | Date;
  hasUnreadMessages?: boolean;
  reportId?: { reportId?: string };
}

interface ActivityApi {
  _id?: string;
  id?: string;
  type?: string;
  title?: string;
  description?: string;
  createdAt?: string | Date;
  reportId?: string;
}

interface DashboardData {
  reports: ReportData[];
  recentMessages: RecentMessage[];
  activities: ActivityItem[];
  notifications: Notification[];
  stats: {
    totalReports: number;
    newReports: number;
    underReviewReports: number;
    awaitingResponseReports: number;
    resolvedReports: number;
    highPriorityReports: number;
    periodComparison: {
      totalReportsChange: number;
      newReportsChange: number;
      resolvedReportsChange: number;
      period: string;
    };
  } | null;
}

interface DashboardState extends DashboardData {
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

interface DashboardActions {
  refreshAll: () => Promise<void>;
  refreshReports: () => Promise<void>;
  refreshMessages: () => Promise<void>;
  refreshNotifications: () => Promise<void>;
  refreshStats: () => Promise<void>;
  markNotificationAsRead: (notificationId: string) => Promise<void>;
  markAllNotificationsAsRead: () => Promise<void>;
  navigateToReport: (reportId: string) => void;
  navigateToMessage: (conversationId: string) => void;
  updateReportStatus: (reportId: string, status: string) => Promise<void>;
  createReport: (reportData: ReportData | Omit<ReportData, 'id' | 'statusColor' | 'progress' | 'progressPercentage' | 'dateSubmitted' | 'lastUpdated'>) => Promise<void>;
}

export function useDashboardData(): DashboardState & DashboardActions {
  const { user, isAuthenticated } = useAuth();
  const [data, setData] = useState<DashboardData>({
    reports: [],
    recentMessages: [],
    activities: [],
    notifications: [],
    stats: null
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  
  // Use ref to track if component is mounted
  const isMountedRef = useRef(true);

  // Fetch reports data
  const fetchReports = useCallback(async (): Promise<ReportData[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchReports: Not authenticated or no user ID');
      return [];
    }

    try {
      console.log('fetchReports: Making API call to /api/reports');
      const response = await apiClient.get<{ success: boolean; data: ReportDocument[] }>('/api/reports');
      console.log('fetchReports: API response:', response);
      
      if (response && response.success && Array.isArray(response.data)) {
        const transformedData = response.data.map(transformReportData);
        console.log('fetchReports: Transformed data:', transformedData);
        return transformedData;
      } else {
        console.log('fetchReports: API response not successful or data not array', response);
        return [];
      }
    } catch (error) {
      console.error('Error fetching reports:', error);
      // Check if it's an authentication error
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        console.log('fetchReports: Authentication error, user may need to re-login');
      }
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch recent messages
  const fetchMessages = useCallback(async (): Promise<RecentMessage[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchMessages: Not authenticated or no user ID');
      return [];
    }

    try {
      console.log('fetchMessages: Making API call to /api/conversations');
      const response = await apiClient.get<{ success: boolean; data: unknown[] }>('/api/conversations?limit=5');
      console.log('fetchMessages: API response:', response);
      
      if (response.success && Array.isArray(response.data)) {
        const transformedData = response.data.map((conv, index) =>
          transformConversationToMessage(conv as ConversationApi, index, user.id)
        );
        console.log('fetchMessages: Transformed data:', transformedData);
        return transformedData;
      }
      console.log('fetchMessages: API response not successful or data not array');
      return [];
    } catch (error) {
      console.error('Error fetching messages:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch activities
  const fetchActivities = useCallback(async (): Promise<ActivityItem[]> => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchActivities: Not authenticated or no user ID');
      return [];
    }
    
    try {
      console.log('fetchActivities: Making API call to /api/dashboard/recent-activity');
      const response = await apiClient.get<{ success: boolean; data: unknown[] }>('/api/dashboard/recent-activity?limit=10');
      console.log('fetchActivities: API response:', response);
      
      if (response.success && Array.isArray(response.data)) {
        const transformedData = response.data.map((activity) => transformActivityData(activity as ActivityApi));
        console.log('fetchActivities: Transformed data:', transformedData);
        return transformedData;
      }
      console.log('fetchActivities: API response not successful or data not array');
      return [];
    } catch (error) {
      console.error('Error fetching activities:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch notifications
  const fetchNotifications = useCallback(async (): Promise<Notification[]> => {
    if (!isAuthenticated || !user?.id) return [];
    
    try {
      const response = await apiClient.get<{ success: boolean; data: Notification[] }>(`/api/notifications?userId=${user.id}&limit=10`);
      if (response.success) {
        return response.data || [];
      }
      return [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return [];
    }
  }, [isAuthenticated, user?.id]);

  // Fetch dashboard stats
  const fetchStats = useCallback(async () => {
    if (!isAuthenticated || !user?.id) {
      console.log('fetchStats: Not authenticated or no user ID');
      return null;
    }
    
    try {
      console.log('fetchStats: Making API call to /api/dashboard/stats');
      const response = await apiClient.get<{ success: boolean; data: unknown }>("/api/dashboard/stats");
      console.log('fetchStats: API response:', response);
      
      if (response && response.success && response.data) {
        console.log('fetchStats: Stats data received:', response.data);
        return response.data;
      } else {
        console.log('fetchStats: API response not successful or no data', response);
        return null;
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
      if (error instanceof Error && error.message.includes('Authentication failed')) {
        console.log('fetchStats: Authentication error, user may need to re-login');
      }
      return null;
    }
  }, [isAuthenticated, user?.id]);

  // Refresh all data
  const refreshAll = useCallback(async () => {
    if (!isMountedRef.current) return;

    console.log('refreshAll: Starting data refresh');
    setIsLoading(true);
    setError(null);

    try {
      console.log('refreshAll: Fetching all data...');
      const [reports, messages, activities, notifications, stats] = await Promise.all([
        fetchReports(),
        fetchMessages(),
        fetchActivities(),
        fetchNotifications(),
        fetchStats()
      ]) as [ReportData[], RecentMessage[], ActivityItem[], Notification[], DashboardData['stats']];

      console.log('refreshAll: All data fetched:', {
        reports: reports.length,
        messages: messages.length,
        activities: activities.length,
        notifications: notifications.length,
        stats: !!stats
      });

      if (isMountedRef.current) {
        setData({
          reports,
          recentMessages: messages,
          activities,
          notifications,
          stats
        });
        setLastUpdated(new Date());
        console.log('refreshAll: Data updated successfully');
      }
    } catch (error) {
      console.error('refreshAll: Error occurred:', error);
      if (isMountedRef.current) {
        setError(error instanceof Error ? error.message : 'Failed to fetch dashboard data');
      }
    } finally {
      if (isMountedRef.current) {
        setIsLoading(false);
        console.log('refreshAll: Loading state set to false');
      }
    }
  }, [fetchReports, fetchMessages, fetchActivities, fetchNotifications, fetchStats]);

  // Individual refresh functions
  const refreshReports = useCallback(async () => {
    const reports = await fetchReports();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, reports }));
      setLastUpdated(new Date());
    }
  }, [fetchReports]);

  const refreshMessages = useCallback(async () => {
    const messages = await fetchMessages();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, recentMessages: messages }));
      setLastUpdated(new Date());
    }
  }, [fetchMessages]);

  const refreshNotifications = useCallback(async () => {
    const notifications = await fetchNotifications();
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, notifications }));
      setLastUpdated(new Date());
    }
  }, [fetchNotifications]);

  const refreshStats = useCallback(async () => {
    const stats = await fetchStats() as DashboardData['stats'];
    if (isMountedRef.current) {
      setData(prev => ({ ...prev, stats }));
      setLastUpdated(new Date());
    }
  }, [fetchStats]);

  // Mark notification as read
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      await apiClient.put(`/api/notifications/${notificationId}/read`, {});
      // Update local state
      setData(prev => ({
        ...prev,
        notifications: prev.notifications.map(notification =>
          notification._id === notificationId
            ? { ...notification, status: 'read' as const }
            : notification
        )
      }));
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }, []);

  // Mark all notifications as read
  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      await apiClient.put('/api/notifications/read-all', {});
      // Update local state
      setData(prev => ({
        ...prev,
        notifications: prev.notifications.map(notification => ({
          ...notification,
          status: 'read' as const
        }))
      }));
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }, []);

  // Navigation actions
  const navigateToReport = useCallback((reportId: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = `/dashboard/whistleblower/my-reports?reportId=${reportId}`;
    }
  }, []);

  const navigateToMessage = useCallback((conversationId: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = `/dashboard/whistleblower/secure-message?conversationId=${conversationId}`;
    }
  }, []);

  // Update report status and refresh related data
  const updateReportStatus = useCallback(async (reportId: string, status: string) => {
    try {
      await apiClient.put(`/api/reports/${reportId}`, { status });
      // Refresh reports and stats to reflect the change
      await Promise.all([refreshReports(), refreshStats()]);
    } catch (error) {
      console.error('Error updating report status:', error);
    }
  }, [refreshReports, refreshStats]);

  // Create new report and refresh data
  const createReport = useCallback(async (reportData: ReportData | Omit<ReportData, 'id' | 'statusColor' | 'progress' | 'progressPercentage' | 'dateSubmitted' | 'lastUpdated'>) => {
    try {
      await apiClient.post('/api/reports', reportData);
      // Refresh all data to reflect the new report
      await refreshAll();
    } catch (error) {
      console.error('Error creating report:', error);
      throw error;
    }
  }, [refreshAll]);

  // Initial data load
  useEffect(() => {
    console.log('useDashboardData: Initial data load effect triggered', {
      isAuthenticated,
      userId: user?.id,
      userRole: user?.role
    });
    
    if (isAuthenticated && user?.id) {
      console.log('useDashboardData: Conditions met, calling refreshAll');
      refreshAll();
    } else {
      console.log('useDashboardData: Conditions not met for data loading');
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id, user?.role, refreshAll]);

  // Real-time data synchronization
  useEffect(() => {
    if (!isAuthenticated || !user?.id) return;

    // Set up event listeners for real-time updates
    const handleReportUpdate = (event: CustomEvent) => {
      console.log('Report updated:', event.detail);
      refreshReports();
      refreshStats();
    };

    const handleNewMessage = (event: CustomEvent) => {
      console.log('New message received:', event.detail);
      refreshMessages();
    };

    const handleNewNotification = (event: CustomEvent) => {
      console.log('New notification:', event.detail);
      refreshNotifications();
    };

    const handleStatusChange = (event: CustomEvent) => {
      console.log('Status changed:', event.detail);
      refreshStats();
    };

    // Add event listeners
    window.addEventListener('report-updated', handleReportUpdate as EventListener);
    window.addEventListener('message-received', handleNewMessage as EventListener);
    window.addEventListener('notification-received', handleNewNotification as EventListener);
    window.addEventListener('status-changed', handleStatusChange as EventListener);

    // Auto-refresh data every 5 minutes
    const autoRefreshInterval = setInterval(() => {
      if (isMountedRef.current) {
        refreshAll();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      window.removeEventListener('report-updated', handleReportUpdate as EventListener);
      window.removeEventListener('message-received', handleNewMessage as EventListener);
      window.removeEventListener('notification-received', handleNewNotification as EventListener);
      window.removeEventListener('status-changed', handleStatusChange as EventListener);
      clearInterval(autoRefreshInterval);
    };
  }, [isAuthenticated, user?.id, refreshReports, refreshMessages, refreshNotifications, refreshStats, refreshAll]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  return {
    ...data,
    isLoading,
    error,
    lastUpdated,
    refreshAll,
    refreshReports,
    refreshMessages,
    refreshNotifications,
    refreshStats,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    navigateToReport,
    navigateToMessage,
    updateReportStatus,
    createReport
  };
}
