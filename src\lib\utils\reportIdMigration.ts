/**
 * Report ID Migration Utility
 * Ensures all existing reports have consistent "WB-" prefixed IDs
 */

import connectDB from '@/lib/db/mongodb';
import Report from '@/lib/db/models/Report';
import { ReportIdGenerator } from './reportIdGenerator';

export class ReportIdMigration {
  /**
   * Migrates all existing reports to use consistent "WB-" prefixed IDs
   * @returns Promise<{ migrated: number; skipped: number; errors: string[] }>
   */
  static async migrateAllReportIds(): Promise<{ migrated: number; skipped: number; errors: string[] }> {
    await connectDB();
    
    const results = {
      migrated: 0,
      skipped: 0,
      errors: [] as string[]
    };
    
    try {
      // Get all reports
      const reports = await Report.find({});
      console.log(`Found ${reports.length} reports to check`);
      
      for (const report of reports) {
        try {
          const currentId = (report as unknown as { reportId: string }).reportId;

          // Check if the report ID already follows the correct format
          if (ReportIdGenerator.validateReportIdFormat(currentId)) {
            results.skipped++;
            continue;
          }
          
          // Generate a new consistent ID
          const newId = await ReportIdGenerator.generateReportId();
          
          // Update the report
          await Report.findByIdAndUpdate(report._id, { reportId: newId });
          
          console.log(`Migrated report ${report._id}: ${currentId} -> ${newId}`);
          results.migrated++;
          
        } catch (error) {
          const errorMsg = `Failed to migrate report ${report._id}: ${error}`;
          console.error(errorMsg);
          results.errors.push(errorMsg);
        }
      }
      
      console.log(`Migration completed: ${results.migrated} migrated, ${results.skipped} skipped, ${results.errors.length} errors`);
      
    } catch (error) {
      const errorMsg = `Migration failed: ${error}`;
      console.error(errorMsg);
      results.errors.push(errorMsg);
    }
    
    return results;
  }
  
  /**
   * Validates that all reports have consistent "WB-" prefixed IDs
   * @returns Promise<{ valid: number; invalid: string[] }>
   */
  static async validateAllReportIds(): Promise<{ valid: number; invalid: string[] }> {
    await connectDB();
    
    const results = {
      valid: 0,
      invalid: [] as string[]
    };
    
    try {
      const reports = await Report.find({}).select('_id reportId');
      
      for (const report of reports) {
        const reportWithId = report as unknown as { reportId: string; _id: string };
        if (ReportIdGenerator.validateReportIdFormat(reportWithId.reportId)) {
          results.valid++;
        } else {
          results.invalid.push(`${reportWithId._id}: ${reportWithId.reportId}`);
        }
      }
      
      console.log(`Validation completed: ${results.valid} valid, ${results.invalid.length} invalid`);
      
    } catch (error) {
      console.error(`Validation failed: ${error}`);
    }
    
    return results;
  }
}