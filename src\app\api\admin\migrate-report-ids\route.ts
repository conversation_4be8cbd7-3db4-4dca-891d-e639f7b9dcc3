import { NextResponse } from 'next/server';
import { ReportIdMigration } from '@/lib/utils/reportIdMigration';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';

export const runtime = 'nodejs';

/**
 * POST /api/admin/migrate-report-ids
 * Migrates all existing reports to use consistent "WB-" prefixed IDs
 * Only accessible by admin users
 */
export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    // Check if user is admin
    if (!request.user || request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    console.log('Starting report ID migration...');
    const results = await ReportIdMigration.migrateAllReportIds();

    return NextResponse.json({
      success: true,
      message: 'Report ID migration completed',
      data: results
    });

  } catch (error) {
    console.error('Report ID migration API error:', error);
    return NextResponse.json(
      { success: false, error: 'Migration failed' },
      { status: 500 }
    );
  }
});

/**
 * GET /api/admin/migrate-report-ids
 * Validates that all reports have consistent "WB-" prefixed IDs
 * Only accessible by admin users
 */
export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    // Check if user is admin
    if (!request.user || request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized. Admin access required.' },
        { status: 403 }
      );
    }

    console.log('Validating report IDs...');
    const results = await ReportIdMigration.validateAllReportIds();

    return NextResponse.json({
      success: true,
      message: 'Report ID validation completed',
      data: results
    });

  } catch (error) {
    console.error('Report ID validation API error:', error);
    return NextResponse.json(
      { success: false, error: 'Validation failed' },
      { status: 500 }
    );
  }
});