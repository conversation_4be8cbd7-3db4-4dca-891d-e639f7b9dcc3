import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ConversationActionDialog } from "@/components/ui/conversation-action-dialog";
import { MoreVertical, Archive, Trash2 } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { useRealTimeUpdates } from "@/hooks/useRealTimeUpdates";
import { useState, useEffect } from "react";
import Link from "next/link";
import { transformConversationToMessage } from "@/lib/utils/dataTransformers";
import { RecentMessage } from "@/lib/types";

interface ApiConversation {
    _id: string;
    reportId?: {
        _id: string;
        reportId: string;
        title: string;
    };
    participants?: Array<{
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    }>;
    status: string;
    lastMessageAt?: string;
}

interface RecentMessagesProps {
    messages?: RecentMessage[];
    isLoading?: boolean;
    onRefresh?: () => Promise<void>;
    onMessageClick?: (messageId: string) => void;
}

export default function RecentMessages({
    messages: propMessages,
    isLoading: propIsLoading,
    onMessageClick
}: RecentMessagesProps = {}) {
    const { user } = useAuth();
    const { isConnected } = useRealTimeUpdates();
    const [messages, setMessages] = useState<RecentMessage[]>(propMessages || []);
    const [isLoading, setIsLoading] = useState(propIsLoading ?? true);
    const [unreadCount, setUnreadCount] = useState(0);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [dialogAction, setDialogAction] = useState<'delete' | 'archive'>('delete');
    const [selectedMessage, setSelectedMessage] = useState<RecentMessage | null>(null);
    const [actionLoading, setActionLoading] = useState(false);
    
    const handleArchive = (messageId: string) => {
        const message = messages.find(m => m.id === messageId);
        if (message) {
            setSelectedMessage(message);
            setDialogAction('archive');
            setDialogOpen(true);
        }
    };
    
    const handleDelete = (messageId: string) => {
        const message = messages.find(m => m.id === messageId);
        if (message) {
            setSelectedMessage(message);
            setDialogAction('delete');
            setDialogOpen(true);
        }
    };
    
    const handleConfirmAction = async () => {
        if (!selectedMessage) return;
        
        setActionLoading(true);
        try {
            const endpoint = `/api/conversations/${selectedMessage.id}/${dialogAction}`;
            const response = await fetch(endpoint, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Content-Type': 'application/json'
                }
            });
            
            if (response.ok) {
                // Remove from local state
                setMessages(prev => prev.filter(m => m.id !== selectedMessage.id));
            }
        } catch (error) {
            console.error(`Error ${dialogAction}ing message:`, error);
        } finally {
            setActionLoading(false);
            setDialogOpen(false);
            setSelectedMessage(null);
        }
    };

    // Update local state when props change
    useEffect(() => {
        if (propMessages) {
            setMessages(propMessages);
            const unreadCount = propMessages.filter(msg => msg.isUnread).length;
            setUnreadCount(unreadCount);
        }
    }, [propMessages]);

    useEffect(() => {
        if (propIsLoading !== undefined) {
            setIsLoading(propIsLoading);
        }
    }, [propIsLoading]);

    // Load messages only if not provided via props
    useEffect(() => {
        if (propMessages || !user?.id) return;

        const loadRecentMessages = async () => {
            try {
                setIsLoading(true);
                const response = await fetch('/api/conversations?limit=3', {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Content-Type': 'application/json'
                    }
                });
                const data = await response.json();

                if (data.success) {
                    const transformedMessages = data.data.map((conv: ApiConversation, index: number) =>
                        transformConversationToMessage(conv, index, user.id)
                    );
                    setMessages(transformedMessages);
                    // Count unread messages
                    const unreadCount = transformedMessages.filter((msg: RecentMessage) => msg.isUnread).length;
                    setUnreadCount(unreadCount);
                }
            } catch (error) {
                console.error('Error loading recent messages:', error);
                // Fallback to empty array
                setMessages([]);
            } finally {
                setIsLoading(false);
            }
        };

        loadRecentMessages();
        
        // Listen for real-time message updates
        const handleMessageUpdate = () => {
            if (!propMessages) {
                loadRecentMessages();
            }
        };
        
        window.addEventListener('new-real-time-message', handleMessageUpdate);
        window.addEventListener('message-sent', handleMessageUpdate);
        
        return () => {
            window.removeEventListener('new-real-time-message', handleMessageUpdate);
            window.removeEventListener('message-sent', handleMessageUpdate);
        };
    }, [user?.id, propMessages, isConnected]);

    return (
        <Card className="h-full">
            <CardHeader className="py-0 flex items-center justify-between gap-2 sm:gap-0">
                <div className="flex items-center gap-2">
                    <CardTitle className="p-0 text-sm sm:text-base text-[#242E2C]">Recent Messages from Investigators</CardTitle>
                    {unreadCount > 0 && (
                        <span className="bg-[#EF4444] text-white text-xs px-2 py-1 rounded-full">
                            {unreadCount} new
                        </span>
                    )}
                </div>
                <Link href="/dashboard/whistleblower/secure-message">
                    <Button
                        variant="ghost"
                        size="sm"
                        aria-label="View all messages"
                        className="text-[#1E4841] text-xs sm:text-sm font-medium hover:bg-[#ECF4E9] self-start sm:self-auto">
                        View All
                    </Button>
                </Link>
            </CardHeader>
            <Separator className="bg-[#F3F4F6]" />
            <CardContent className="space-y-3 sm:space-y-4">
                {isLoading ? (
                    <div className="space-y-3">
                        {[...Array(3)].map((_, index) => (
                            <div key={index} className="border border-gray-200 rounded-lg p-3 sm:p-4 animate-pulse">
                                <div className="flex items-center gap-2 mb-2">
                                    <div className="h-4 bg-gray-200 rounded w-20"></div>
                                    <div className="h-4 bg-gray-200 rounded w-16"></div>
                                </div>
                                <div className="flex items-start gap-2 sm:gap-3">
                                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-full"></div>
                                    <div className="flex-1">
                                        <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                                        <div className="h-4 bg-gray-200 rounded w-full"></div>
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                ) : messages.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8 text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                        </div>
                        <h3 className="text-sm font-medium text-gray-900 mb-1">No recent messages</h3>
                        <p className="text-xs text-gray-500 mb-4">Messages from investigators will appear here</p>
                        <Link href="/dashboard/secure-message">
                            <Button
                                variant="outline"
                                size="sm"
                                className="text-[#1E4841] border-[#1E4841] hover:bg-[#ECF4E9]">
                                View All Messages
                            </Button>
                        </Link>
                    </div>
                ) : (
                    messages.map((message, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-3 sm:p-4 space-y-2 sm:space-y-3">
                        <div className="mb-1 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                            <div className="flex items-center gap-2">
                                <span className="text-xs font-medium text-[#6B7280]">Report ID: {message.id}</span>
                                {message.isUnread && (
                                    <div className="-mt-0.5 w-1.5 h-1.5 bg-[#EF4444] rounded-full"></div>
                                )}
                            </div>
                            <p className="text-xs text-[#6B7280]">{message.time}</p>
                        </div>
                        <div className="flex items-start gap-2 sm:gap-3">
                            <Avatar className="w-6 h-6 sm:w-8 sm:h-8">
                                <AvatarFallback className="bg-blue-100 text-blue-600 text-xs sm:text-sm">
                                    {message.avatar}
                                </AvatarFallback>
                            </Avatar>
                            <div className="flex-1">
                                <div className="flex items-center gap-2 mb-1">
                                    <span className="font-medium text-sm sm:text-base text-[#111827]">{message.investigator}</span>
                                </div>
                                <p className="text-xs sm:text-sm font-normal text-[#6B7271] leading-relaxed">{message.message}</p>
                            </div>
                        </div>
                        <div className="flex justify-end">
                            <div className="flex items-center gap-2">
                                <Button
                                    aria-label="Open Secure Chat"
                                    size="sm"
                                    className="bg-[#1E4841] hover:bg-[#1E4841]/90 text-white font-normal text-xs sm:text-sm px-3 sm:px-4"
                                    onClick={() => onMessageClick ? onMessageClick(message.id) : window.location.href = '/dashboard/whistleblower/secure-message'}>
                                    Open Secure Chat
                                </Button>
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                            <MoreVertical className="h-4 w-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => handleArchive(message.id)}>
                                            <Archive className="mr-2 h-4 w-4" />
                                            Archive
                                        </DropdownMenuItem>
                                        <DropdownMenuItem 
                                            onClick={() => handleDelete(message.id)}
                                            className="text-red-600 focus:text-red-600"
                                        >
                                            <Trash2 className="mr-2 h-4 w-4" />
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </div>
                    ))
                )}
            </CardContent>
            
            <ConversationActionDialog
                open={dialogOpen}
                onOpenChange={setDialogOpen}
                action={dialogAction}
                conversationName={selectedMessage?.investigator || 'Unknown'}
                onConfirm={handleConfirmAction}
                isLoading={actionLoading}
            />
        </Card>
    );
}