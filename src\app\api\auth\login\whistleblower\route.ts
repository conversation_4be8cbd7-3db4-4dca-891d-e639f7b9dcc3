import { NextRequest, NextResponse } from 'next/server';
import { DataService } from '@/lib/db/dataService';
import { loginSchema } from '@/lib/schemas';
import connectDB from '@/lib/db/mongodb';
import jwt from 'jsonwebtoken';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Validation failed',
          details: validationResult.error.issues
        },
        { status: 400 }
      );
    }

    const { email, password, remember } = validationResult.data;

    // Authenticate with database users
    await connectDB();
    const dbAuthResult = await DataService.authenticateUser(email, password);
    
    if (dbAuthResult.user) {
      // Database user found and authenticated
      const user = dbAuthResult.user;
      
      // Check if user has whistleblower or investigator role (not admin)
      if (user.role === 'admin') {
        return NextResponse.json(
          { 
            success: false, 
            error: 'Access denied. Please use the admin login page for administrator accounts.' 
          },
          { status: 403 }
        );
      }
      
      // Generate JWT token
      const token = jwt.sign(
        { 
          userId: user._id,
          email: user.email,
          role: user.role,
          companyId: user.companyId
        },
        process.env.JWT_SECRET || 'your-secret-key-change-in-production',
        { expiresIn: '24h' }
      );
      
      return NextResponse.json({
        success: true,
        message: 'Whistleblower login successful',
        token,
        user: {
          id: user._id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          fullName: user.fullName,
          role: user.role,
          companyId: user.companyId,
          isActive: user.isActive,
          lastLogin: user.lastLogin
        },
        remember
      });
    }

    // If authentication failed, return error
    return NextResponse.json(
      { success: false, error: dbAuthResult.error || 'Invalid email or password' },
      { status: 401 }
    );

  } catch (error) {
    console.error('Whistleblower login error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}