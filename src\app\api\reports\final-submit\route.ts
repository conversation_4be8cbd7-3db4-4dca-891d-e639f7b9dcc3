import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import { ObjectId } from 'mongodb';
import jwt from 'jsonwebtoken';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Get JWT token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.replace('Bearer ', '') || request.cookies.get('auth_token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify JWT token
    let decoded: { userId: string; email: string };
    try {
      decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as { userId: string; email: string };
    } catch {
      return NextResponse.json(
        { success: false, error: 'Invalid token' },
        { status: 401 }
      );
    }

    const mongoose = await connectDB();
    const db = mongoose.connection.db;
    
    if (!db) {
      return NextResponse.json(
        { success: false, error: 'Database connection failed' },
        { status: 500 }
      );
    }
    
    // Generate case number
    const currentYear = new Date().getFullYear();
    const timestamp = Date.now().toString().slice(-6);
    const caseNumber = `WB-${currentYear}${timestamp}`;
    
    // Create the report document
    const reportData = {
      ...body,
      caseNumber,
      userId: new ObjectId(decoded.userId),
      userEmail: decoded.email,
      submittedAt: new Date(),
      status: 'New',
      priority: body.urgencyLevel || 'Medium',
      step: 'final',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert the report
    const result = await db.collection('reports').insertOne(reportData);
    
    if (result.insertedId) {
      // Create notification for admin users
      const adminUsers = await db.collection('users').find({ role: 'admin' }).toArray();
      
      const notifications = adminUsers.map(admin => ({
        userId: admin._id,
        type: 'new_report',
        title: 'New Whistleblower Report Submitted',
        message: `A new report "${body.title}" has been submitted and requires review.`,
        data: {
          reportId: result.insertedId.toString(),
          caseNumber,
          reportTitle: body.title,
          priority: body.urgencyLevel || 'Medium'
        },
        read: false,
        createdAt: new Date()
      }));

      if (notifications.length > 0) {
        await db.collection('notifications').insertMany(notifications);
      }

      return NextResponse.json({
        success: true,
        data: {
          id: result.insertedId.toString(),
          caseNumber,
          message: 'Report submitted successfully'
        }
      });
    } else {
      return NextResponse.json(
        { success: false, error: 'Failed to submit report' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Final submit error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
