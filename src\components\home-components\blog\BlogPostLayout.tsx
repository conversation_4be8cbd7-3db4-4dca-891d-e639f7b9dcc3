import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Calendar, Clock, ArrowLeft, Bookmark } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import Header from '@/components/home-components/shared/Header';
import Footer from '@/components/home-components/shared/Footer';
import BlogContent from './BlogContent';
import BlogSidebar from './BlogSidebar';
import BlogPostGrid from './BlogPostGrid';
import { getRelatedBlogPosts } from '@/lib/mockData';
import { BlogCard } from '@/lib/types';

interface BlogPostLayoutProps {
  blogPost: BlogCard;
  children?: React.ReactNode;
  relatedPosts?: BlogCard[];
}

/**
 * BlogPostLayout provides a standardized layout for all blog posts
 * This ensures consistent design across all blog posts
 */
const BlogPostLayout: React.FC<BlogPostLayoutProps> = ({ blogPost, children, relatedPosts }) => {
  // If relatedPosts are not provided, get them from the blogPost
  const posts = relatedPosts || getRelatedBlogPosts(blogPost.id);
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 pt-20">
        {/* Hero Section */}
        <section className="relative w-full h-[250px] sm:h-[300px] md:h-[400px] lg:h-[500px]">
          <Image
            src={blogPost.image}
            alt={blogPost.title}
            fill
            priority
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end">
            <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-6 sm:py-8 md:py-12">
              <Link href="/blog" className="inline-flex items-center text-white mb-4 hover:underline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Link>
              <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-2 sm:mb-4 max-w-4xl">
                {blogPost.title}
              </h1>
              <div className="flex items-center gap-3 sm:gap-6 text-white justify-between sm:justify-start">
                <div className="flex items-center gap-3 sm:gap-6">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    <span className="text-sm">{blogPost.date}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">{blogPost.readTime}</span>
                  </div>
                </div>
                <Button variant="ghost" size="icon" className="lg:hidden text-white hover:text-white hover:bg-white/20 rounded-full">
                  <Bookmark className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </section>

        <div className="container mx-auto px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[180px] py-6 sm:py-8 md:py-12">
          <div className="flex flex-col lg:flex-row gap-8 md:gap-10 lg:gap-12">
            {/* Main Content */}
            <div className="w-full lg:w-2/3">
              {/* Author Info */}
              <div className="flex items-center gap-3 sm:gap-4 mb-6 sm:mb-8">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={blogPost.author.image} alt={blogPost.author.name} />
                  <AvatarFallback>{blogPost.author.initials}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold text-[#1E4841]">{blogPost.author.name}</p>
                  <p className="text-sm text-gray-500">Author & {blogPost.category} Expert</p>
                </div>
              </div>

              {/* Blog Content */}
              {blogPost.content ? (
                <BlogContent content={blogPost.content} />
              ) : (
                <p className="text-lg text-gray-700">{blogPost.description}</p>
              )}

              {/* Additional content if provided */}
              {children}
            </div>

            {/* Sidebar - using standardized BlogSidebar component */}
            <BlogSidebar
              category={blogPost.category}
              relatedPosts={posts}
              tags={blogPost.tags ?? []}
            />
          </div>
        </div>
        
        {/* Related Posts Section */}
        {posts.length > 0 && (
          <BlogPostGrid 
            posts={posts} 
            title="More Articles You Might Like" 
            showViewAllButton={true} 
          />
        )}
      </main>
      <Footer />
    </div>
  );
};

export default BlogPostLayout;