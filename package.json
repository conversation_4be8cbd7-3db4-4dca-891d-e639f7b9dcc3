{"name": "whistleblower-new", "version": "0.1.0", "private": true, "scripts": {"dev": "tsx server.ts", "dev:next": "next dev -p 3002 --turbopack", "dev:ws-standalone": "tsx src/lib/websocket/devServer.ts", "dev:socket-standalone": "tsx src/lib/websocket/socketServer.ts", "build": "next build", "build:no-lint": "cross-env SKIP_LINT=true next build", "start": "NODE_ENV=production tsx server.ts", "start:next": "next start -p 3002", "lint": "next lint", "db:seed": "tsx scripts/run.ts seed", "db:verify": "tsx scripts/run.ts verify", "db:clean": "tsx scripts/run.ts clean"}, "type": "module", "overrides": {"react-is": "^19.1.0"}, "dependencies": {"@auth/core": "^0.40.0", "@hookform/resolvers": "^5.2.1", "@lexical/html": "^0.34.0", "@lexical/link": "^0.34.0", "@lexical/list": "^0.34.0", "@lexical/mark": "^0.34.0", "@lexical/markdown": "^0.34.0", "@lexical/react": "^0.34.0", "@lexical/rich-text": "^0.34.0", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-context-menu": "^2.2.16", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.16", "@radix-ui/react-navigation-menu": "^1.2.14", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "@vercel/analytics": "^1.5.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "express-rate-limit": "^8.0.1", "input-otp": "^1.4.2", "jsonwebtoken": "^9.0.2", "lexical": "^0.34.0", "lucide-react": "^0.539.0", "mongodb": "^6.18.0", "mongoose": "^8.17.1", "next": "^15.4.6", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^7.0.5", "react": "^19.1.1", "react-day-picker": "^9.8.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "recharts": "^3.1.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.17"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.12", "@types/bcrypt": "^6.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.3.0", "@types/nodemailer": "^7.0.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "dotenv": "^17.2.1", "eslint": "^9.33.0", "eslint-config-next": "^15.4.6", "tailwindcss": "^4.1.12", "tailwindcss-animate": "^1.0.7", "tsx": "^4.20.4", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2"}}