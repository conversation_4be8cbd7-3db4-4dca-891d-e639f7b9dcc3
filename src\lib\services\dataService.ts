// This file has been deprecated in favor of the proper database-integrated NotificationService
// All notification functionality is now handled by:
// - @/lib/services/notificationService.ts (for business logic)
// - @/lib/db/dataService.ts (for database operations)

// Re-export the proper NotificationService to maintain compatibility
export { NotificationService } from './notificationService';

// Legacy compatibility - redirect to proper service
import { NotificationService } from './notificationService';

export class LegacyNotificationService {
  async getNotificationsByUserId(userId: string) {
    return await NotificationService.getUserNotifications(userId);
  }

  async getLegacyNotifications() {
    console.warn('getLegacyNotifications is deprecated. Use proper database queries instead.');
    return [];
  }

  async markAsRead(notificationId: string): Promise<void> {
    await NotificationService.markAsRead(notificationId);
  }

  async markAllAsRead(userId: string): Promise<void> {
    await NotificationService.markAllAsRead(userId);
  }
}

export const notificationService = new LegacyNotificationService();

// Deprecated - use NotificationService directly
export default LegacyNotificationService;