"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { Home, Timer, LogOut, X, LockKeyhole, Archive, Trash2, MoreVertical, Search, Send, Paperclip, Eye, CheckCheck, ChevronLeft } from "lucide-react";
import Header from "@/components/dashboard-components/Header";
import { useAuth } from "@/hooks/useAuth";
import { useSocket, MessageWithSender } from "@/hooks/useSocket";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import LexicalEditor, { LexicalEditorRef } from "@/components/ui/lexical-editor";
import { EditorState } from 'lexical';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { markConversationAsRead } from "@/lib/utils/messageIndicators";
import logger from "@/lib/utils/logger";
import { apiClient } from "@/lib/utils/apiClient";

// Simple ConfirmationDialog component
const ConfirmationDialog = ({ open, onOpenChange, title, description, confirmText, onConfirm, variant = 'default' }: {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    title: string;
    description: string;
    confirmText: string;
    onConfirm: () => void;
    variant?: 'default' | 'destructive';
}) => {
    if (!open) return null;
    
    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 className="text-lg font-semibold mb-2">{title}</h3>
                <p className="text-gray-600 mb-4">{description}</p>
                <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => onOpenChange(false)}>
                        Cancel
                    </Button>
                    <Button 
                        variant={variant === 'destructive' ? 'destructive' : 'default'}
                        onClick={() => {
                            onConfirm();
                            onOpenChange(false);
                        }}
                    >
                        {confirmText}
                    </Button>
                </div>
            </div>
        </div>
    );
};

interface ApiConversation {
    _id: string;
    reportId?: {
        _id: string;
        reportId: string;
        title: string;
    };
    participants?: Array<{
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    }>;
    status: string;
    lastMessageAt?: string;
    isEncrypted: boolean;
    priority?: string;
    category?: string;
}

// Use a union type that can accommodate both message formats
type MessageData = {
    id: string;
    content: string;
    timestamp: string;
    html?: string;
    attachments?: File[];
    conversationId?: string;
    senderId?: string;
    htmlContent?: string;
    messageType?: 'text' | 'file' | 'system';
    readBy?: Array<{
        userId: string;
        readAt: Date;
    }>;
    reactions?: Array<{
        userId: string;
        emoji: string;
        createdAt: Date;
    }>;
    replyTo?: string;
    isDeleted?: boolean;
    deletedAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    _id?: string;
} | {
    _id: string;
    conversationId: string;
    content: string;
    htmlContent?: string;
    messageType: 'text' | 'file' | 'system';
    attachments?: Array<{
        fileName: string;
        fileUrl: string;
        fileSize: number;
        mimeType: string;
    }>;
    readBy: Array<{
        userId: string;
        readAt: Date;
    }>;
    reactions?: Array<{
        userId: string;
        emoji: string;
        createdAt: Date;
    }>;
    replyTo?: string;
    isDeleted?: boolean;
    deletedAt?: Date;
    createdAt: Date;
    updatedAt: Date;
    senderId: {
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    } | string;
    id?: string;
    timestamp?: string;
    html?: string;
}

interface ConversationData {
    id: string;
    _id?: string;
    name: string;
    caseId: string;
    reportId?: {
        _id: string;
        reportId: string;
        title: string;
    };
    participants?: Array<{
        _id: string;
        firstName?: string;
        lastName?: string;
        role: string;
    }>;
    lastMessage: string;
    time: string;
    isUnread: boolean;
    isOnline: boolean;
    isTyping: boolean;
    avatarBg?: string;
    status?: 'active' | 'closed' | 'archived';
    lastMessageAt?: Date;
    isEncrypted?: boolean;
}

const ConversationItem = ({
    conversation,
    isActive,
    onClick,
    onArchive,
    onDelete
}: {
    conversation: ConversationData;
    isActive: boolean;
    onClick: () => void;
    onArchive: () => void;
    onDelete: () => void;
}) => {
    const baseClasses = "flex flex-col justify-between group cursor-pointer transition-all duration-200 p-3 border-b border-gray-100 hover:bg-gray-50";
    const activeClasses = isActive ? "bg-[#ECF4E9] border-l-4 border-l-[#1E4841]" : "bg-white";
    const unreadClasses = conversation.isUnread ? "font-medium" : "";
    const classNames = `${baseClasses} ${activeClasses} ${unreadClasses}`.trim();
    
    const avatarBg = conversation.avatarBg || "bg-[#BBF49C]";

    return (
        <div
            className={classNames}
            onClick={onClick}
            role="button"
            tabIndex={0}
            aria-label={`Conversation with ${conversation.name} about ${conversation.caseId}`}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onClick();
                }
            }}
        >
            <div className="flex items-start gap-3 mb-2">
                <div className="relative">
                    <div className={`h-10 w-10 rounded-full flex items-center justify-center ${avatarBg} shadow-sm transition-transform group-hover:scale-105`}>
                        <span className="text-sm font-medium text-[#1E4841]">
                            {conversation.name.split(' ').map(n => n[0]).join('').slice(0, 2)}
                        </span>
                    </div>
                    {conversation.isOnline && (
                        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full animate-pulse"></div>
                    )}
                </div>
                <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 min-w-0 flex-1">
                            <p className="text-sm font-semibold text-[#111827] truncate group-hover:text-[#1E4841] transition-colors">
                                {conversation.name}
                            </p>
                            {conversation.isOnline && (
                                <span className="text-xs bg-green-100 text-green-600 font-medium px-1.5 py-0.5 rounded-full">Online</span>
                            )}
                        </div>
                        <div className="flex items-center gap-2">
                            <p className="text-xs text-[#6B7280] group-hover:text-[#1E4841] transition-colors">{conversation.time}</p>
                            {conversation.isUnread && (
                                <div className="flex items-center gap-1">
                                    <div className="w-2.5 h-2.5 bg-[#EF4444] rounded-full animate-pulse"></div>
                                    <span className="text-xs text-[#EF4444] font-medium">New</span>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs font-medium text-[#6B7280] bg-gray-100 px-2 py-0.5 rounded-full">{conversation.caseId}</span>
                        {conversation.status && (
                            <span className={`text-xs font-medium px-2 py-0.5 rounded-full ${
                                conversation.status === 'active' ? 'bg-green-100 text-green-600' :
                                conversation.status === 'closed' ? 'bg-gray-100 text-gray-600' :
                                'bg-yellow-100 text-yellow-600'
                            }`}>
                                {conversation.status.charAt(0).toUpperCase() + conversation.status.slice(1)}
                            </span>
                        )}
                    </div>
                </div>
            </div>
            <div className="flex items-center justify-between">
                <p className="text-sm text-[#4B5563] truncate flex-1 mr-2 break-words overflow-wrap-anywhere">
                    {conversation.isTyping ? (
                        <span className="text-green-600 italic">Typing...</span>
                    ) : (
                        conversation.lastMessage
                    )}
                </p>
                <div className="flex items-center gap-1">
                    <LockKeyhole className="w-3 h-3 text-[#1E4841]" />
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100">
                                <MoreVertical className="w-3 h-3" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                            <DropdownMenuItem onClick={onArchive}>
                                <Archive className="w-4 h-4 mr-2" />
                                Archive
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={onDelete} className="text-red-600">
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </div>
        </div>
    );
};

export default function SecureMessage() {
    const router = useRouter();
    const { user, isLoading } = useAuth();
    const [conversations, setConversations] = useState<ConversationData[]>([]);
    const [isLoadingConversations, setIsLoadingConversations] = useState(true);
    const [activeConversationId, setActiveConversationId] = useState<string>("");
    const [searchQuery, setSearchQuery] = useState<string>("");
    const [newMessage, setNewMessage] = useState<string>("");
    const [newMessageHtml, setNewMessageHtml] = useState<string>("");
    const [isLoadingMessage, setIsLoadingMessage] = useState<boolean>(false);
    const [autoLogoutTime, setAutoLogoutTime] = useState<number>(15 * 60);
    const [showAutoLogout, setShowAutoLogout] = useState<boolean>(false);

    const [refreshTrigger, setRefreshTrigger] = useState(0);
    const [sentMessages, setSentMessages] = useState<Record<string, MessageData[]>>({});
    const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
    const [clearTrigger, setClearTrigger] = useState(0);
    const [typingUsers, setTypingUsers] = useState<Set<string>>(new Set());
    const [conversationMessages, setConversationMessages] = useState<Record<string, (MessageData | MessageWithSender)[]>>({});
    const [loadingMessages, setLoadingMessages] = useState<Record<string, boolean>>({});
    const [showArchiveConfirm, setShowArchiveConfirm] = useState(false);
    const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
    const [conversationToArchive, setConversationToArchive] = useState<string | null>(null);
    const [conversationToDelete, setConversationToDelete] = useState<string | null>(null);
    const messagesContainerRef = useRef<HTMLDivElement>(null);
    const editorRef = useRef<LexicalEditorRef>(null);

    // WebSocket integration for real-time messaging
    const {
        isConnected,
        onlineUsers,
        sendMessage: socketSendMessage,
        joinConversation,
        startTyping,
        stopTyping
    } = useSocket(
        user?.id || null,
        user?.role || null,
        (message) => {
            // Handle new message received
            console.log('New message received:', message);

            // Add the new message to the conversation messages
            if (message.conversationId && conversationMessages[message.conversationId]) {
                setConversationMessages(prev => ({
                    ...prev,
                    [message.conversationId]: [...(prev[message.conversationId] || []), message]
                }));
            }

            // Refresh conversations to show new message
            setRefreshTrigger(prev => prev + 1);
        },
        (data) => {
            // Handle typing indicator
            if (data.conversationId === activeConversationId && data.userId !== user?.id) {
                setTypingUsers(prev => {
                    const newSet = new Set(prev);
                    if (data.isTyping) {
                        newSet.add(data.userId);
                    } else {
                        newSet.delete(data.userId);
                    }
                    return newSet;
                });
            }
        },
        (status) => {
            // Handle user status change
            console.log('User status changed:', status);
        }
    );

    const resetAutoLogoutTimer = useCallback(() => {
        setAutoLogoutTime(15 * 60);
        setShowAutoLogout(false);
    }, []);

    // Load messages for a specific conversation
    const loadConversationMessages = useCallback(async (conversationId: string) => {
        if (!conversationId || conversationMessages[conversationId] || loadingMessages[conversationId]) {
            return; // Already loaded or loading
        }

        setLoadingMessages(prev => ({ ...prev, [conversationId]: true }));

        try {
            const response = await apiClient.get(`/api/messages?conversationId=${conversationId}`);
            if (response.success) {
                setConversationMessages(prev => ({
                    ...prev,
                    [conversationId]: Array.isArray(response.data) ? response.data : []
                }));
            }
        } catch (error) {
            console.error('Error loading conversation messages:', error);
            // Set empty array to prevent infinite loading
            setConversationMessages(prev => ({
                ...prev,
                [conversationId]: []
            }));
        } finally {
            setLoadingMessages(prev => ({ ...prev, [conversationId]: false }));
        }
    }, [conversationMessages, loadingMessages]);

    // Auto-logout functionality
    useEffect(() => {
        let inactivityTimeout: NodeJS.Timeout;

        const handleUserActivity = () => {
            resetAutoLogoutTimer();

            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }

            inactivityTimeout = setTimeout(() => {
                setShowAutoLogout(true);
            }, 2 * 60 * 1000);
        };

        const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

        events.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });

        handleUserActivity();

        return () => {
            events.forEach(event => {
                document.removeEventListener(event, handleUserActivity, true);
            });
            if (inactivityTimeout) {
                clearTimeout(inactivityTimeout);
            }
        };
    }, [resetAutoLogoutTimer]);

    useEffect(() => {
        if (!showAutoLogout) return;

        const timer = setInterval(() => {
            setAutoLogoutTime(prev => {
                if (prev <= 1) {
                    router.push('/logout');
                    return 0;
                }
                return prev - 1;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [router, showAutoLogout]);

    const formatAutoLogoutTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    // Load conversations on component mount
    useEffect(() => {
        const loadConversations = async () => {
            if (!user?.id) return;
            
            try {
                setIsLoadingConversations(true);
                const data = await apiClient.get(`/api/conversations?userId=${user.id}`);
                
                if (data.success && data.data && Array.isArray(data.data)) {
                    // Transform API data to match ConversationData interface
                    const transformedConversations = (data.data as ApiConversation[]).map((conv: ApiConversation) => ({
                        id: conv._id,
                        _id: conv._id,
                        name: conv.participants
                            ?.filter((p) => p._id !== user.id)
                            .map((p) => `${p.firstName} ${p.lastName}`)
                            .join(', ') || 'Unknown',
                        caseId: conv.reportId?.reportId || 'No Case ID',
                        ...(conv.reportId && { reportId: conv.reportId }),
                        ...(conv.participants && { participants: conv.participants }),
                        lastMessage: 'Start a secure conversation',
                        time: conv.lastMessageAt ? new Date(conv.lastMessageAt).toLocaleTimeString('en-US', {
                            hour: 'numeric',
                            minute: '2-digit',
                            hour12: true
                        }) : 'Now',
                        isUnread: false, // TODO: Implement read status
                        isOnline: conv.participants?.some(p => p._id !== user.id && onlineUsers.has(p._id)) || false,
                        isTyping: false,
                        status: (conv.status === 'active' || conv.status === 'closed' || conv.status === 'archived') 
                            ? conv.status 
                            : 'active' as 'active' | 'closed' | 'archived',
                        lastMessageAt: conv.lastMessageAt ? new Date(conv.lastMessageAt) : new Date(),
                        isEncrypted: conv.isEncrypted
                    }));
                    setConversations(transformedConversations);
                }
            } catch (error) {
                console.error('Error loading conversations:', error);
            } finally {
                setIsLoadingConversations(false);
            }
        };

        loadConversations();
    }, [user?.id, refreshTrigger, onlineUsers]);

    // Refresh conversation data from persistent source
    useEffect(() => {
        const handleVisibilityChange = () => {
            if (!document.hidden) {
                setRefreshTrigger(prev => prev + 1);
            }
        };

        const handleFocus = () => {
            setRefreshTrigger(prev => prev + 1);
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);
        window.addEventListener('focus', handleFocus);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
            window.removeEventListener('focus', handleFocus);
        };
    }, []);

    const handleConversationSelect = async (conversationId: string) => {
        setActiveConversationId(conversationId);

        // Join the WebSocket conversation room
        if (isConnected) {
            joinConversation(conversationId);
        }

        // Clear typing users when switching conversations
        setTypingUsers(new Set());

        // Load messages for this conversation (lazy loading)
        await loadConversationMessages(conversationId);

        // Mark conversation as read and update the sidebar counter
        try {
            await markConversationAsRead(conversationId);
            // Refresh the conversation data to reflect the persistent changes
            setRefreshTrigger(prev => prev + 1);
        } catch (error) {
            logger.error('Error marking conversation as read:', error);
        }
    };

    const handleArchiveConversation = (conversationId: string) => {
        setConversationToArchive(conversationId);
        setShowArchiveConfirm(true);
    };

    const confirmArchiveConversation = () => {
        if (!conversationToArchive) return;
        
        setConversations(prev => prev.filter(conv => conv.id !== conversationToArchive));
        if (activeConversationId === conversationToArchive) {
            setActiveConversationId("");
        }
        logger.info(`Archived conversation ${conversationToArchive}`);
        setShowArchiveConfirm(false);
        setConversationToArchive(null);
    };

    const handleDeleteConversation = (conversationId: string) => {
        setConversationToDelete(conversationId);
        setShowDeleteConfirm(true);
    };

    const confirmDeleteConversation = () => {
        if (!conversationToDelete) return;
        
        setConversations(prev => prev.filter(conv => conv.id !== conversationToDelete));
        if (activeConversationId === conversationToDelete) {
            setActiveConversationId("");
        }
        logger.info(`Deleted conversation ${conversationToDelete}`);
        setShowDeleteConfirm(false);
        setConversationToDelete(null);
    };

    const scrollToBottom = useCallback(() => {
        if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop = messagesContainerRef.current.scrollHeight;
        }
    }, []);

    const handleSendMessage = async () => {
        if (!newMessage.trim()) return;

        const messageContent = newMessage.trim();
        const timestamp = new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        setIsLoadingMessage(true);

        const newSentMessage = {
            id: `sent_${Date.now()}`,
            content: messageContent,
            html: newMessageHtml, // Include HTML content for rich formatting
            timestamp: timestamp,
            ...(attachedFiles.length > 0 && { attachments: [...attachedFiles] })
        };
        setSentMessages(prev => ({
            ...prev,
            [activeConversationId]: [...(prev[activeConversationId] || []), newSentMessage]
        }));

        setNewMessage("");
        setNewMessageHtml("");
        setAttachedFiles([]);
        handleClearEditor();

        // Add message to local state immediately for better UX
        const optimisticMessage: MessageData = {
            id: `temp_${Date.now()}`,
            content: messageContent,
            timestamp: new Date().toISOString(),
            html: newMessageHtml,
            ...(user?.id && { senderId: user.id }),
            ...(activeConversationId && { conversationId: activeConversationId }),
            messageType: 'text'
        };

        if (activeConversationId) {
            setConversationMessages(prev => ({
                ...prev,
                [activeConversationId]: [...(prev[activeConversationId] || []), optimisticMessage]
            }));
        }

        // Send message via WebSocket with HTML content
        if (isConnected && activeConversationId) {
            socketSendMessage({
                conversationId: activeConversationId,
                content: messageContent,
                htmlContent: newMessageHtml,
                messageType: 'text'
            });
        }

        setIsLoadingMessage(false);
        resetAutoLogoutTimer();

        setTimeout(() => {
            scrollToBottom();
        }, 100);
    };

    const handleEditorChange = (newEditorState: EditorState) => {
        newEditorState.read(() => {
            const root = newEditorState._nodeMap.get('root');
            if (root) {
                const textContent = root.getTextContent();
                setNewMessage(textContent);

                // Also capture HTML content for rich text formatting
                try {
                    if (editorRef.current) {
                        const htmlContent = editorRef.current.getHTML();
                        setNewMessageHtml(htmlContent);
                    } else {
                        setNewMessageHtml(textContent);
                    }
                } catch (error) {
                    console.error('Error generating HTML from editor state:', error);
                    setNewMessageHtml(textContent); // Fallback to plain text
                }

                // Send typing indicator when user is typing
                if (textContent.trim().length > 0 && activeConversationId && isConnected) {
                    startTyping(activeConversationId);
                } else if (activeConversationId && isConnected) {
                    stopTyping(activeConversationId);
                }
            }
        });

        resetAutoLogoutTimer();
    };

    const handleEditorKeyDown = (event: KeyboardEvent): boolean => {
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            event.preventDefault();
            event.stopPropagation();
            handleSendMessage();
            return true;
        }
        return false;
    };

    const handleClearEditor = () => {
        setClearTrigger(prev => prev + 1);
    };

    const removeAttachedFile = (index: number) => {
        setAttachedFiles(prev => prev.filter((_, i) => i !== index));
    };

    // Auto-scroll to bottom when messages change
    useEffect(() => {
        scrollToBottom();
    }, [sentMessages, typingUsers, activeConversationId, scrollToBottom]);

    // Refresh conversations when online users change
    useEffect(() => {
        if (conversations.length > 0) {
            setRefreshTrigger(prev => prev + 1);
        }
    }, [onlineUsers, conversations.length]);

    const filteredConversations = conversations.filter(conv =>
        conv.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.caseId.toLowerCase().includes(searchQuery.toLowerCase()) ||
        conv.lastMessage.toLowerCase().includes(searchQuery.toLowerCase())
    );

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
            </div>
        );
    }

    if (!user) {
        router.push('/login');
        return null;
    }

    return (
        <div className="flex flex-col min-h-screen bg-gray-50">
            <Header />
            
            {/* Auto-logout banner */}
            {showAutoLogout && (
                <div className="bg-red-600 text-white px-4 py-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <Timer className="w-4 h-4" />
                        <span className="text-sm">
                            Auto-logout in {formatAutoLogoutTime(autoLogoutTime)} due to inactivity
                        </span>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700"
                            onClick={resetAutoLogoutTimer}
                        >
                            Stay Logged In
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700"
                            onClick={() => router.push('/logout')}
                        >
                            <LogOut className="w-4 h-4 mr-1" />
                            Logout Now
                        </Button>
                        <Button
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-red-700 p-1"
                            onClick={() => setShowAutoLogout(false)}
                        >
                            <X className="w-4 h-4" />
                        </Button>
                    </div>
                </div>
            )}
            
            <div className="flex-1 flex flex-col p-6">
                {/* Breadcrumb */}
                <div className="mb-6">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard/whistleblower" className="flex items-center gap-2">
                                    <Home className="w-4 h-4" />
                                    Dashboard
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Secure Messages</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                </div>

                {/* Page Header */}
                <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-900">Secure Messages</h1>
                    <p className="text-gray-600 mt-1">
                        Communicate securely with investigators and administrators about your reports.
                    </p>
                </div>

                {/* Enhanced Messaging Interface */}
                <div className="flex-1 min-h-0">
                    {isLoadingConversations ? (
                        <div className="flex items-center justify-center h-96">
                            <div className="text-center">
                                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4" />
                                <p className="text-gray-600">Loading conversations...</p>
                            </div>
                        </div>
                    ) : (
                        <div className="flex h-[calc(100vh-12rem)] bg-white rounded-lg shadow-lg overflow-hidden">
                            {/* Enhanced Conversations Sidebar */}
                            <div className="w-1/3 border-r border-gray-200 flex flex-col">
                                {/* Search Bar */}
                                <div className="p-4 border-b border-gray-200">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                                        <Input
                                            placeholder="Search conversations..."
                                            value={searchQuery}
                                            onChange={(e) => setSearchQuery(e.target.value)}
                                            className="pl-10"
                                        />
                                    </div>
                                </div>

                                {/* Conversations List */}
                                <div className="flex-1 overflow-y-auto">
                                    {filteredConversations.length === 0 ? (
                                        <div className="flex items-center justify-center h-32 text-gray-500">
                                            <div className="text-center">
                                                <p>No conversations found</p>
                                                <p className="text-sm">Start by reporting an incident</p>
                                            </div>
                                        </div>
                                    ) : (
                                        filteredConversations.map((conversation) => (
                                            <ConversationItem
                                                key={conversation.id}
                                                conversation={conversation}
                                                isActive={conversation.id === activeConversationId}
                                                onClick={() => handleConversationSelect(conversation.id)}
                                                onArchive={() => handleArchiveConversation(conversation.id)}
                                                onDelete={() => handleDeleteConversation(conversation.id)}
                                            />
                                        ))
                                    )}
                                </div>
                            </div>

                            {/* Whistleblower Messaging Interface */}
                            <div className="flex-1 flex flex-col">
                                {activeConversationId ? (
                                    <>
                                        {/* Chat Header */}
                                        <div className="p-4 border-b border-gray-200 bg-white">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-3">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => setActiveConversationId("")}
                                                        className="lg:hidden"
                                                    >
                                                        <ChevronLeft className="w-4 h-4" />
                                                    </Button>
                                                    <div className="flex items-center gap-3">
                                                        <div className={`h-10 w-10 rounded-full flex items-center justify-center ${conversations.find(c => c.id === activeConversationId)?.avatarBg || "bg-[#BBF49C]"}`}>
                                                            <span className="text-sm font-medium text-[#1E4841]">
                                                                {conversations.find(c => c.id === activeConversationId)?.name?.split(' ').map(n => n[0]).join('').slice(0, 2)}
                                                            </span>
                                                        </div>
                                                        <div>
                                                            <div className="flex items-center gap-2">
                                                                <h3 className="font-medium text-gray-900">
                                                                    {conversations.find(c => c.id === activeConversationId)?.name}
                                                                </h3>
                                                                {conversations.find(c => c.id === activeConversationId)?.isOnline && (
                                                                    <div className="w-2 h-2 bg-green-500 rounded-full" title="Online" />
                                                                )}
                                                            </div>
                                                            <p className="text-sm text-gray-500">
                                                                Direct conversation • Case: {conversations.find(c => c.id === activeConversationId)?.caseId}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <LockKeyhole className="w-4 h-4 text-[#1E4841]" />
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" size="sm">
                                                                <MoreVertical className="w-4 h-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent>
                                                            <DropdownMenuItem>
                                                                <Eye className="w-4 h-4 mr-2" />
                                                                View Report Details
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem onClick={() => handleArchiveConversation(activeConversationId)}>
                                                                <Archive className="w-4 h-4 mr-2" />
                                                                Archive
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Messages Area */}
                                        <div className="flex-1 overflow-y-auto p-4 space-y-4" ref={messagesContainerRef}>
                                            {/* Loading state */}
                                            {loadingMessages[activeConversationId] && (
                                                <div className="flex justify-center items-center py-8">
                                                    <div className="w-6 h-6 border-2 border-[#BBF49C] border-t-transparent rounded-full animate-spin" />
                                                    <span className="ml-2 text-sm text-gray-500">Loading messages...</span>
                                                </div>
                                            )}

                                            {/* Existing messages from API */}
                                            {!loadingMessages[activeConversationId] && conversationMessages[activeConversationId]?.map((message) => {
                                                // Helper function to get message ID
                                                const getMessageId = (msg: MessageData | MessageWithSender): string => {
                                                    return ('_id' in msg ? msg._id : '') || ('id' in msg ? msg.id : '') || 'unknown';
                                                };
                                                
                                                // Helper function to get sender ID 
                                                const getSenderId = (msg: MessageData | MessageWithSender): string => {
                                                    if (typeof msg.senderId === 'string') {
                                                        return msg.senderId;
                                                    } else if (typeof msg.senderId === 'object' && msg.senderId) {
                                                        return msg.senderId._id;
                                                    }
                                                    return '';
                                                };
                                                
                                                // Helper function to get HTML content
                                                const getHtmlContent = (msg: MessageData | MessageWithSender): string | undefined => {
                                                    return ('html' in msg ? msg.html : '') || ('htmlContent' in msg ? msg.htmlContent : '');
                                                };
                                                
                                                // Helper function to get timestamp
                                                const getTimestamp = (msg: MessageData | MessageWithSender): string => {
                                                    if ('timestamp' in msg && msg.timestamp) return msg.timestamp;
                                                    if ('createdAt' in msg && msg.createdAt) {
                                                        return typeof msg.createdAt === 'string' ? msg.createdAt : msg.createdAt.toISOString();
                                                    }
                                                    return new Date().toISOString();
                                                };
                                                
                                                return (
                                                <div key={getMessageId(message)} className={`flex ${getSenderId(message) === user?.id ? 'justify-end' : 'justify-start'}`}>
                                                    <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg break-words ${getSenderId(message) === user?.id ? 'bg-[#1E4841] text-white' : 'bg-gray-100 text-gray-900'}`}>
                                                        {getHtmlContent(message) ? (
                                                            <div
                                                                className="text-sm prose prose-sm max-w-none break-words overflow-wrap-anywhere"
                                                                dangerouslySetInnerHTML={{ __html: getHtmlContent(message) || '' }}
                                                            />
                                                        ) : (
                                                            <p className="text-sm break-words overflow-wrap-anywhere">{message.content}</p>
                                                        )}
                                                        <p className="text-xs mt-1 opacity-70">
                                                            {new Date(getTimestamp(message)).toLocaleTimeString('en-US', {
                                                                hour: 'numeric',
                                                                minute: '2-digit',
                                                                hour12: true
                                                            })}
                                                        </p>
                                                    </div>
                                                </div>
                                                );
                                            })}

                                            {/* Empty state */}
                                            {!loadingMessages[activeConversationId] &&
                                             conversationMessages[activeConversationId] &&
                                             conversationMessages[activeConversationId].length === 0 && (
                                                <div className="flex justify-center items-center py-8">
                                                    <p className="text-sm text-gray-500">No messages yet. Start the conversation!</p>
                                                </div>
                                            )}

                                            {/* Sent messages */}
                                            {sentMessages[activeConversationId]?.map((message) => (
                                                <div key={message.id} className="flex justify-end">
                                                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-[#1E4841] text-white break-words">
                                                        {message.html ? (
                                                            <div
                                                                className="text-sm prose prose-sm max-w-none prose-invert break-words overflow-wrap-anywhere"
                                                                dangerouslySetInnerHTML={{ __html: message.html }}
                                                            />
                                                        ) : (
                                                            <p className="text-sm break-words overflow-wrap-anywhere">{message.content}</p>
                                                        )}
                                                        {message.attachments && message.attachments.length > 0 && (
                                                            <div className="mt-2 space-y-1">
                                                                {message.attachments.map((file, index) => (
                                                                    <div key={index} className="flex items-center gap-2 text-xs">
                                                                        <Paperclip className="w-3 h-3" />
                                                                        <span>{'name' in file ? file.name : file.fileName}</span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        )}
                                                        <div className="flex items-center justify-between mt-1">
                                                            <p className="text-xs opacity-70">{message.timestamp}</p>
                                                            <CheckCheck className="w-3 h-3" />
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}

                                            {/* Typing indicator - only show when OTHER users are typing */}
                                            {typingUsers.size > 0 && (
                                                <div className="flex justify-start">
                                                    <div className="max-w-xs lg:max-w-md px-4 py-2 rounded-lg bg-gray-100">
                                                        <div className="flex items-center gap-1">
                                                            <div className="flex gap-1">
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                                                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                                            </div>
                                                            <span className="text-xs text-gray-500 ml-2">
                                                                {typingUsers.size === 1 ? 'Someone is typing...' : `${typingUsers.size} people are typing...`}
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>

                                        {/* Message Composer */}
                                        <div className="p-4 border-t border-gray-200 bg-white">
                                            {/* File attachments preview */}
                                            {attachedFiles.length > 0 && (
                                                <div className="mb-3 flex flex-wrap gap-2">
                                                    {attachedFiles.map((file, index) => (
                                                        <div key={index} className="flex items-center gap-2 bg-gray-100 px-3 py-1 rounded-lg text-sm max-w-48">
                                                            <Paperclip className="w-3 h-3 flex-shrink-0" />
                                                            <span className="truncate flex-1 min-w-0" title={file.name}>{file.name}</span>
                                                            <button
                                                                onClick={() => removeAttachedFile(index)}
                                                                className="text-red-500 hover:text-red-700"
                                                            >
                                                                <X className="w-3 h-3" />
                                                            </button>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}

                                            <div className="flex items-end gap-2">
                                                <div className="flex-1">
                                                    <LexicalEditor
                                                        ref={editorRef}
                                                        placeholder=""
                                                        onChange={handleEditorChange}
                                                        onKeyDown={handleEditorKeyDown}
                                                        clearTrigger={clearTrigger}
                                                        className="min-h-[60px] max-h-32 border border-gray-300 rounded-lg p-3 focus:ring-2 focus:ring-[#1E4841] focus:border-transparent overflow-y-auto resize-none"
                                                    />
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        onClick={handleSendMessage}
                                                        disabled={!newMessage.trim() || isLoadingMessage}
                                                        className="bg-[#1E4841] hover:bg-[#2a5d54] text-white px-4 py-2"
                                                    >
                                                        {isLoadingMessage ? (
                                                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                                        ) : (
                                                            <Send className="w-4 h-4" />
                                                        )}
                                                    </Button>
                                                </div>
                                            </div>
                                            <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                                                <LockKeyhole className="w-3 h-3" />
                                                This conversation is end-to-end encrypted
                                            </p>
                                        </div>
                                    </>
                                ) : (
                                    <div className="flex-1 flex items-center justify-center bg-gray-50">
                                        <div className="text-center">
                                            <div className="w-16 h-16 bg-[#ECF4E9] rounded-full flex items-center justify-center mx-auto mb-4">
                                                <LockKeyhole className="w-8 h-8 text-[#1E4841]" />
                                            </div>
                                            <h3 className="text-lg font-medium text-gray-900 mb-2">
                                                Secure Messaging
                                            </h3>
                                            <p className="text-gray-600 max-w-sm">
                                                Select a conversation to start secure messaging with investigators and administrators about your reports.
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>
            
            <ConfirmationDialog
                open={showArchiveConfirm}
                onOpenChange={setShowArchiveConfirm}
                title="Archive Conversation"
                description={`Are you sure you want to archive the conversation with ${conversations.find(c => c.id === conversationToArchive)?.name}?`}
                confirmText="Archive"
                onConfirm={confirmArchiveConversation}
            />
            
            <ConfirmationDialog
                open={showDeleteConfirm}
                onOpenChange={setShowDeleteConfirm}
                title="Delete Conversation"
                description={`Are you sure you want to delete the conversation with ${conversations.find(c => c.id === conversationToDelete)?.name}? This action cannot be undone.`}
                confirmText="Delete"
                onConfirm={confirmDeleteConversation}
                variant="destructive"
            />
        </div>
    );
}