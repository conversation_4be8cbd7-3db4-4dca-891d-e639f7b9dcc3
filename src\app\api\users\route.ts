import { NextResponse } from 'next/server';
import { User } from '@/lib/db/models';
import { withCompanyIsolation, AuthenticatedRequest } from '@/lib/middleware/auth';
import connectDB from '@/lib/db/mongodb';

export const runtime = 'nodejs';

export const GET = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Only admins can view all users
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }
    
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const isActive = searchParams.get('isActive');
    const companyId = searchParams.get('companyId') || request.user.companyId;
    
    const query: Record<string, unknown> = {};
    
    // Filter by company for company isolation
    if (companyId) {
      query.companyId = companyId;
    }
    
    if (role) {
      query.role = role;
    }
    
    if (isActive !== null) {
      query.isActive = isActive === 'true';
    }
    
    const users = await User.find(query)
      .select('-hashedPassword -sessionToken -twoFactor')
      .populate('companyId', 'name')
      .sort({ createdAt: -1 })
      .limit(100);
    
    return NextResponse.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Users GET API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});

export const POST = withCompanyIsolation(async (request: AuthenticatedRequest) => {
  try {
    await connectDB();
    
    if (!request.user) {
      return NextResponse.json(
        { success: false, error: 'User not authenticated' },
        { status: 401 }
      );
    }
    
    // Only admins can create users
    if (request.user.role !== 'admin') {
      return NextResponse.json(
        { success: false, error: 'Insufficient permissions' },
        { status: 403 }
      );
    }
    
    const userData = await request.json();
    
    if (!userData.email || !userData.firstName || !userData.lastName) {
      return NextResponse.json(
        { success: false, error: 'Email, first name, and last name are required' },
        { status: 400 }
      );
    }
    
    // Check if user already exists
    const existingUser = await User.findOne({ email: userData.email });
    if (existingUser) {
      return NextResponse.json(
        { success: false, error: 'User with this email already exists' },
        { status: 409 }
      );
    }
    
    // Set company ID from authenticated user if not provided
    if (!userData.companyId) {
      userData.companyId = request.user.companyId;
    }
    
    const user = new User({
      ...userData,
      isActive: userData.isActive ?? true,
      role: userData.role || 'whistleblower'
    });
    
    const savedUser = await user.save();
    
    // Remove sensitive data from response
    const userResponse = savedUser.toObject() as Record<string, unknown>;

    // Remove sensitive fields if they exist
    const sensitiveFields = ['hashedPassword', 'sessionToken', 'twoFactor'];
    const safeUserResponse = { ...userResponse };
    sensitiveFields.forEach(field => {
      delete safeUserResponse[field];
    });
    
    return NextResponse.json({
      success: true,
      data: safeUserResponse
    }, { status: 201 });
  } catch (error) {
    console.error('Users POST API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
});