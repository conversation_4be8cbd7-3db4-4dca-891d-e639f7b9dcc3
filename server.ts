import { createServer } from 'http';
import { parse } from 'url';
import next from 'next';
import { Server } from 'socket.io';
import type { Socket } from 'socket.io';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = parseInt(process.env.PORT || '3002', 10);

// When using middleware `hostname` and `port` must be provided below
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

interface UserData {
  userId: string;
  userRole: string;
  socketId: string;
  authenticatedAt: Date;
}

app.prepare().then(() => {
  const httpServer = createServer(async (req, res) => {
    try {
      // Be sure to pass `true` as the second argument to `url.parse`.
      // This tells it to parse the query portion of the URL.
      const parsedUrl = parse(req.url!, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  });

  // Initialize Socket.IO (WebSocket only)
  const io = new Server(httpServer, {
    cors: {
      origin: process.env.NODE_ENV === 'production' 
        ? process.env.NEXTAUTH_URL 
        : "http://localhost:3002",
      methods: ["GET", "POST"],
      credentials: true
    },
    transports: ['websocket'], // Use only WebSocket transport
    allowEIO3: true // Allow fallback for compatibility
  });

  // Make io instance globally available for DataService
  (global as { io?: Server }).io = io;

  // Store connected users and their conversations
  const connectedUsers = new Map<string, UserData>();
  const typingUsers = new Map<string, Set<string>>();
  const authenticatedUsers = new Set<string>(); // Track authenticated users to prevent duplicate auth

  io.on('connection', (socket: Socket) => {
    console.log('User connected:', socket.id);
    let isAuthenticated = false;

    // Handle user authentication and joining
    socket.on('authenticate', (userData: { userId: string; userRole?: string; role?: string }) => {
      // Prevent duplicate authentication for the same socket
      if (isAuthenticated) {
        console.log(`Socket ${socket.id} already authenticated for user ${userData.userId}`);
        return;
      }

      // Validate authentication data
      const userRole = userData.userRole || userData.role; // Support both formats
      if (!userData.userId || !userRole) {
        console.error('Invalid authentication data:', userData);
        socket.emit('auth_error', { message: 'Invalid authentication data' });
        return;
      }

      // Check if user is already connected on another socket
      const existingUserSocket = Array.from(connectedUsers.entries())
        .find(([, user]) => user.userId === userData.userId);
      
      if (existingUserSocket) {
        console.log(`User ${userData.userId} already connected on socket ${existingUserSocket[0]}, disconnecting old connection`);
        const [oldSocketId, oldUser] = existingUserSocket;
        const oldSocket = io.sockets.sockets.get(oldSocketId);
        if (oldSocket) {
          oldSocket.disconnect();
        }
        connectedUsers.delete(oldSocketId);
      }

      connectedUsers.set(socket.id, {
        userId: userData.userId,
        userRole: userRole,
        socketId: socket.id,
        authenticatedAt: new Date()
      });
      
      isAuthenticated = true;
      authenticatedUsers.add(userData.userId);
      
      // Notify others about user coming online
      socket.broadcast.emit('user_status_change', {
        userId: userData.userId,
        isOnline: true,
        timestamp: new Date()
      });

      // Send authentication success
      socket.emit('authenticated', { 
        success: true, 
        userId: userData.userId,
        socketId: socket.id 
      });

      console.log(`User ${userData.userId} authenticated with role ${userRole}`);
    });

    // Handle joining conversation rooms
    socket.on('join_conversation', (conversationId: string) => {
      socket.join(conversationId);
      console.log(`Socket ${socket.id} joined conversation ${conversationId}`);
    });

    // Handle leaving conversation rooms
    socket.on('leave_conversation', (conversationId: string) => {
      socket.leave(conversationId);
      console.log(`Socket ${socket.id} left conversation ${conversationId}`);
    });

    // Handle new messages
    socket.on('send_message', async (messageData: { conversationId: string; content: string; messageType?: string; attachments?: string[] }) => {
      try {
        // Broadcast the message to all users in the conversation
        socket.to(messageData.conversationId).emit('new_message', {
          ...messageData,
          timestamp: new Date(),
          socketId: socket.id
        });

        console.log('Message sent to conversation:', messageData.conversationId);
      } catch (error) {
        console.error('Error handling message:', error);
        socket.emit('message_error', { error: 'Failed to send message' });
      }
    });

    // Handle typing indicators
    socket.on('typing_start', (data: { conversationId: string; userId: string }) => {
      const { conversationId, userId } = data;
      
      if (!typingUsers.has(conversationId)) {
        typingUsers.set(conversationId, new Set());
      }
      
      typingUsers.get(conversationId)!.add(userId);
      
      // Broadcast typing indicator to others in the conversation
      socket.to(conversationId).emit('user_typing', {
        conversationId,
        userId,
        isTyping: true
      });
    });

    socket.on('typing_stop', (data: { conversationId: string; userId: string }) => {
      const { conversationId, userId } = data;
      
      if (typingUsers.has(conversationId)) {
        typingUsers.get(conversationId)!.delete(userId);
        
        if (typingUsers.get(conversationId)!.size === 0) {
          typingUsers.delete(conversationId);
        }
      }
      
      // Broadcast stop typing to others in the conversation
      socket.to(conversationId).emit('user_typing', {
        conversationId,
        userId,
        isTyping: false
      });
    });

    // Handle message read receipts
    socket.on('mark_message_read', (data: { messageId: string; conversationId: string; userId: string }) => {
      const { messageId, conversationId, userId } = data;
      
      // Broadcast read receipt to others in the conversation
      socket.to(conversationId).emit('message_read', {
        messageId,
        userId,
        readAt: new Date()
      });
    });

    // Handle real-time events from useRealTimeUpdates hook
    socket.on('realtime_event', (eventData: { type: string; data: { isOnline?: boolean; targetUserId?: string; conversationId?: string; status?: string; error?: string }; userId?: string; timestamp?: string }) => {
      try {
        const { type, data, userId, timestamp } = eventData;
        
        switch (type) {
          case 'user_status':
            // Broadcast user status updates
            socket.broadcast.emit('realtime_event', {
              type: 'user_status',
              data: {
                userId,
                isOnline: data.isOnline,
                timestamp: timestamp || new Date().toISOString()
              },
              timestamp: timestamp || new Date().toISOString()
            });
            break;
            
          case 'notification':
            // Broadcast notifications
            if (data.targetUserId) {
              // Send to specific user
              const targetSocket = Array.from(connectedUsers.entries())
                .find(([_, user]) => user.userId === data.targetUserId)?.[0];
              if (targetSocket) {
                io.to(targetSocket).emit('realtime_event', eventData);
              }
            } else {
              // Broadcast to all
              socket.broadcast.emit('realtime_event', eventData);
            }
            break;
            
          case 'typing':
            // Handle typing indicators
            if (data.conversationId) {
              socket.to(data.conversationId).emit('realtime_event', eventData);
            }
            break;
            
          default:
            // Broadcast other events
            socket.broadcast.emit('realtime_event', eventData);
        }
        
        console.log('Real-time event processed:', type, userId);
      } catch (error) {
        console.error('Error handling real-time event:', error);
        socket.emit('realtime_event', {
          type: 'connection',
          data: { status: 'error', error: 'Failed to process event' },
          timestamp: new Date().toISOString()
        });
      }
    });

    // Handle ping/pong for connection health
    socket.on('ping', (data: { timestamp: string }) => {
      socket.emit('realtime_event', {
        type: 'connection',
        data: { type: 'pong', timestamp: data.timestamp },
        timestamp: new Date().toISOString()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason: string) => {
      const user = connectedUsers.get(socket.id);
      
      if (user) {
        // Notify others about user going offline
        socket.broadcast.emit('user_status_change', {
          userId: user.userId,
          isOnline: false,
          timestamp: new Date()
        });

        // Clean up typing indicators
        for (const [conversationId, typingSet] of typingUsers.entries()) {
          if (typingSet.has(user.userId)) {
            typingSet.delete(user.userId);
            socket.to(conversationId).emit('user_typing', {
              conversationId,
              userId: user.userId,
              isTyping: false
            });
          }
          
          // Clean up empty typing sets
          if (typingSet.size === 0) {
            typingUsers.delete(conversationId);
          }
        }

        // Clean up user from authenticated set
        authenticatedUsers.delete(user.userId);
        connectedUsers.delete(socket.id);
        
        console.log(`User ${user.userId} disconnected (reason: ${reason})`);
      } else {
        console.log(`Socket ${socket.id} disconnected without authentication (reason: ${reason})`);
      }
    });
  });

  httpServer
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(`> Ready on http://${hostname}:${port}`);
      console.log('> Socket.IO server initialized');
    });
});