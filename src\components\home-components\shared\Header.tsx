"use client";
import Image from "next/image";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { ChevronDown, Menu } from "lucide-react";
import { useState, useEffect, memo, useRef, useCallback } from "react";
import { LOGIN_ITEMS, SIGNUP_ITEMS, PRODUCT_ITEMS } from "@/lib/mockData";
import { usePathname } from 'next/navigation';
import NavLink from "@/components/home-components/shared/header-components/NavLink";
import DropdownContent from "@/components/home-components/shared/header-components/DropdownContent";
import NavDropdown from "@/components/home-components/shared/header-components/NavDropdown";
import MobileDropdown from "@/components/home-components/shared/header-components/MobileDropdown";
import MobileDropdownMenu from "@/components/home-components/shared/header-components/MobileDropdownMenu";

const Header = () => {
    const pathname = usePathname();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
    const [openMobileDropdown, setOpenMobileDropdown] = useState<string | null>(null);
    const [isScrolled, setIsScrolled] = useState(false);
    const [isVisible, setIsVisible] = useState(true);
    const lastScrollY = useRef(0);

    useEffect(() => {
        let ticking = false;
        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    const currentScrollY = window.scrollY;
                    setIsScrolled(currentScrollY > 0);
                    setIsVisible(currentScrollY <= 0 || currentScrollY < lastScrollY.current);
                    lastScrollY.current = currentScrollY;
                    ticking = false;
                });
                ticking = true;
            }
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const closeMobileMenu = useCallback(() => {
        setIsMobileMenuOpen(false);
        setOpenMobileDropdown(null);
    }, []);

    const toggleMobileDropdown = useCallback((dropdown: string) => {
        setOpenMobileDropdown(prev => prev === dropdown ? null : dropdown);
    }, []);

    return (
        <>
            <header className={`fixed w-full top-0 z-50 transition-all duration-300
                ${isScrolled ? 'bg-[#ECF4E9]/95 backdrop-blur-sm shadow-md' : 'bg-[#ECF4E9]'}
                ${isVisible ? 'translate-y-0' : '-translate-y-full'}
                px-4 sm:px-6 md:px-8 lg:px-16 xl:px-24 py-5 flex justify-between items-center`}>
                <div className="flex gap-10">
                    <div className="flex items-center">
                        <Link href="/">
                            <Image
                                src="/logo.svg"
                                alt="Logo"
                                width={83}
                                height={37}
                                className="rounded-full hover:scale-105 transition-transform duration-300"
                                style={{ width: '80', height: 'auto' }}
                                priority
                            />
                        </Link>
                    </div>

                    <nav className="hidden md:flex space-x-4 items-center text-[#6B7271]">
                        <NavLink href="/">
                            <span className={`${pathname === '/' ? 'font-bold' : ''}`}>Home</span>
                        </NavLink>

                        <NavDropdown
                            title="Products"
                            items={PRODUCT_ITEMS}
                            width="w-[800px]"
                            isActive={pathname.startsWith('/products')}
                        />

                        <NavLink href="/pricing">
                            <span className={`${pathname === '/pricing' ? 'font-bold' : ''}`}>Pricing</span>
                        </NavLink>

                        <NavLink href="/about">
                            <span className={`${pathname === '/about' ? 'font-bold' : ''}`}>About</span>
                        </NavLink>

                        <NavLink href="/blog">
                            <span className={`${pathname === '/blog' ? 'font-bold' : ''}`}>Blog</span>
                        </NavLink>

                        <NavLink href="/contact">
                            <span className={`${pathname === '/contact' ? 'font-bold' : ''}`}>Contact</span>
                        </NavLink>
                    </nav>
                </div>

                <div className="hidden xl:flex space-x-6 items-center">
                    <Link href="/start-trial-free">
                        <Button variant="outline" className="bg-[#1E4841] text-white hover:bg-green-900 hover:text-gray-100 py-5 transition-all duration-300 font-medium flex items-center gap-2">
                            <Image
                                src="/desktop/shared/header/start-trail-free.svg"
                                alt="start trail free icon"
                                height={12}
                                width={17}
                                priority
                            />
                            Start Trial Free
                        </Button>
                    </Link>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="text-[#1E4841] hover:text-gray-900 hover:bg-[#BBF49C50] py-5 transition-colors duration-300 group font-semibold">
                                Login <ChevronDown className="ml-1 transition-transform duration-300 group-data-[state=open]:rotate-180" size={16} />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent sideOffset={10} align="start" className="w-[180px]">
                            <DropdownContent items={LOGIN_ITEMS} />
                        </DropdownMenuContent>
                    </DropdownMenu>

                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button className="text-[#1E4841] bg-[#BBF49C] hover:bg-lime-200 hover:text-gray-900 py-5 transition-all duration-300 group font-semibold">
                                Signup <ChevronDown className="ml-1 transition-transform duration-300 group-data-[state=open]:rotate-180" size={16} />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent sideOffset={10} align="start" className="w-[150px]">
                            <DropdownContent items={SIGNUP_ITEMS} />
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>

                {/* Mobile Menu for md+ devices (Dropdown) */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button
                            variant="ghost"
                            className="hidden md:flex xl:hidden p-2 hover:bg-green-100 transition-colors duration-300"
                            aria-label="Open menu"
                        >
                            <Menu className="h-6 w-6 text-[#1E4841]" />
                        </Button>
                    </DropdownMenuTrigger>
                    <MobileDropdownMenu />
                </DropdownMenu>

                {/* Mobile Menu for sm and below (Sheet) */}
                <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
                    <SheetTrigger asChild>
                        <Button
                            variant="ghost"
                            className="md:hidden p-2 hover:bg-green-100 transition-colors duration-300"
                            aria-label="Open menu"
                        >
                            <Menu className="h-6 w-6 text-[#1E4841]" />
                        </Button>
                    </SheetTrigger>
                    <SheetContent side="top" className="bg-[#ECF4E9] md:hidden">
                        <SheetHeader className="pb-0 mb-0">
                            <SheetTitle className="text-[#1E4841]">Navigation Menu</SheetTitle>
                            <SheetDescription className="sr-only">
                                Mobile navigation menu with links to all main sections of the website
                            </SheetDescription>
                        </SheetHeader>
                        {/* Navigation Links */}
                        <div className="">
                            <div className="border-b border-green-200 py-1">
                                <Link href="/" onClick={closeMobileMenu} className="block px-4 py-1 text-[#6B7271] font-medium hover:bg-green-100 transition-colors duration-300">
                                    Home
                                </Link>
                            </div>

                            <MobileDropdown
                                title="Products"
                                items={PRODUCT_ITEMS}
                                isOpen={openMobileDropdown === 'products'}
                                onToggle={() => toggleMobileDropdown('products')}
                                onItemClick={closeMobileMenu}
                            />

                            <div className="border-b border-green-200 py-1">
                                <Link href="/pricing" onClick={closeMobileMenu} className="block px-4 py-1 text-[#6B7271] font-medium hover:bg-green-100 transition-colors duration-300">
                                    Pricing
                                </Link>
                            </div>

                            <div className="border-b border-green-200 py-1">
                                <Link href="/about" onClick={closeMobileMenu} className="block px-4 py-1 text-[#6B7271] font-medium hover:bg-green-100 transition-colors duration-300">
                                    About
                                </Link>
                            </div>

                            <div className="border-b border-green-200 py-1">
                                <Link href="/blog" onClick={closeMobileMenu} className="block px-4 py-1 text-[#6B7271] font-medium hover:bg-green-100 transition-colors duration-300">
                                    Blog
                                </Link>
                            </div>

                            <div className="border-b border-green-200 py-1">
                                <Link href="/contact" onClick={closeMobileMenu} className="block px-4 py-1 text-[#6B7271] font-medium hover:bg-green-100 transition-colors duration-300">
                                    Contact
                                </Link>
                            </div>
                        </div>

                        {/* Action Buttons */}
                        <div className="">
                            <Link href="/start-trial-free" onClick={closeMobileMenu}>
                                <Button className="w-full bg-[#1E4841] text-white hover:bg-green-900 hover:text-gray-100 transition-all duration-300 font-medium justify-center flex items-center gap-2">
                                    <Image
                                        src="/desktop/shared/header/start-trail-free.svg"
                                        alt="start trail free icon"
                                        height={12}
                                        width={17}
                                    />
                                    Start Trial Free
                                </Button>
                            </Link>

                            <MobileDropdown
                                title="Login"
                                items={LOGIN_ITEMS}
                                isOpen={openMobileDropdown === 'login'}
                                onToggle={() => toggleMobileDropdown('login')}
                                onItemClick={closeMobileMenu}
                            />

                            <MobileDropdown
                                title="Signup"
                                items={SIGNUP_ITEMS}
                                isOpen={openMobileDropdown === 'signup'}
                                onToggle={() => toggleMobileDropdown('signup')}
                                onItemClick={closeMobileMenu}
                            />
                        </div>
                    </SheetContent>
                </Sheet>
            </header>
        </>
    );
};

export default memo(Header);