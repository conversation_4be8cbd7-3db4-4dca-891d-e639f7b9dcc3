"use client";
import Image from "next/image";
import Link from "next/link";
import { memo } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { NavItem } from "@/lib/types";

interface NavCardProps extends NavItem {
    cta?: string;
    onItemClick?: () => void;
}

const NavCard = memo(({ href, title, description, cta, onItemClick }: NavCardProps) => {
    const getBackgroundClasses = (title: string) => {
        switch (title) {
            case 'Whistleblower Portal':
                return 'bg-[#F7FFF2]';
            case 'Investigator Portal':
                return 'bg-[#E9FFDD]';
            case 'Admin Portal':
                return 'bg-[#D6FFBD]';
            // About Items
            case 'Company':
                return 'bg-[#F7FFF2]';
            case 'Team':
                return 'bg-[#E9FFDD]';
            case 'Careers':
                return 'bg-[#D6FFBD]';
            // Blog Items
            case 'Technology':
                return 'bg-[#F7FFF2]';
            case 'Industry News':
                return 'bg-[#E9FFDD]';
            case 'Company Updates':
                return 'bg-[#D6FFBD]';
            default:
                return 'bg-[#F7FFF2]';
        }
    };

    return (
        <Card className={`${getBackgroundClasses(title)} justify-between transition-all duration-300 group border-0 shadow-none rounded-lg`}>
            <CardHeader>
                <Image
                    src="/logo.svg"
                    alt="Logo"
                    width={83}
                    height={37}
                    className="rounded-full hover:scale-105 transition-transform duration-300 w-12 h-auto"
                    priority
                />
                <CardTitle className="text-lg text-[#242E2C] font-extrabold">{title}</CardTitle>
                <CardDescription className="text-xs text-[#4B5563] font-medium leading-relaxed">
                    {description}
                </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
                <Link
                    href={href}
                    className="inline-flex items-center text-sm text-[#1E4841] font-semibold hover:text-[#2A5D54] transition-all duration-300 group-hover:translate-x-1"
                    {...(onItemClick && { onClick: onItemClick })}
                >
                    {cta || 'Learn More'} →
                </Link>
            </CardContent>
        </Card>
    );
});

NavCard.displayName = 'NavCard';

export default NavCard;