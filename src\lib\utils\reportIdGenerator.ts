/**
 * Report ID Generator Utility
 * Ensures consistent "WB-" prefixed report IDs throughout the application
 */

import Report from '@/lib/db/models/Report';
import connectDB from '@/lib/db/mongodb';

export class ReportIdGenerator {
  private static readonly PREFIX = 'WB-';
  
  /**
   * Generates a unique report ID with format: WB-YYYY-NNNN (or more digits if needed)
   * @returns Promise<string> - The generated report ID
   */
  static async generateReportId(): Promise<string> {
    await connectDB();
    
    const currentYear = new Date().getFullYear();
    const reportCount = await Report.countDocuments();
    const sequentialNumber = (reportCount + 1).toString().padStart(4, '0');
    
    return `${this.PREFIX}${currentYear}-${sequentialNumber}`;
  }
  
  /**
   * Validates if a report ID follows the correct format
   * @param reportId - The report ID to validate
   * @returns boolean - True if valid format
   */
  static validateReportIdFormat(reportId: string): boolean {
    const pattern = /^WB-\d{4}-\d{4,}$/;
    return pattern.test(reportId);
  }
  
  /**
   * Extracts the year from a report ID
   * @param reportId - The report ID
   * @returns number | null - The year or null if invalid format
   */
  static extractYear(reportId: string | undefined): number | null {
    if (!reportId || typeof reportId !== 'string') {
      return null;
    }

    // At this point, reportId is guaranteed to be a string
    const validReportId: string = reportId;

    if (!this.validateReportIdFormat(validReportId)) {
      return null;
    }

    const yearMatch = validReportId.match(/WB-(\d{4})-\d{4,}/);
    return yearMatch && yearMatch[1] ? parseInt(yearMatch[1], 10) : null;
  }
  
  /**
   * Extracts the sequential number from a report ID
   * @param reportId - The report ID
   * @returns number | null - The sequential number or null if invalid format
   */
  static extractSequentialNumber(reportId: string | undefined): number | null {
    if (!reportId || typeof reportId !== 'string') {
      return null;
    }
    
    if (!this.validateReportIdFormat(reportId as string)) {
      return null;
    }

    const numberMatch = reportId!.match(/WB-\d{4}-(\d{4,})/);
    return numberMatch && numberMatch[1] ? parseInt(numberMatch[1], 10) : null;
  }
  
  /**
   * Ensures a report ID has the correct "WB-" prefix format
   * If the ID doesn't follow the format, it generates a new one
   * @param reportId - The report ID to normalize
   * @returns Promise<string> - The normalized report ID
   */
  static async normalizeReportId(reportId?: string): Promise<string> {
    if (reportId && typeof reportId === 'string' && this.validateReportIdFormat(reportId)) {
      return reportId;
    }
    
    // Generate a new ID if the provided one is invalid or missing
    return await this.generateReportId();
  }
  
  /**
   * Gets the prefix used for all report IDs
   * @returns string - The report ID prefix
   */
  static getPrefix(): string {
    return this.PREFIX;
  }
}