import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/db/mongodb';
import Blog from '@/lib/db/models/Blog';

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    await connectDB();
    
    const blog = await Blog.findOne({ 
      $or: [
        { slug: params.slug },
        { _id: params.slug }
      ]
    });
    
    if (!blog) {
      return NextResponse.json(
        { success: false, error: 'Blog not found' },
        { status: 404 }
      );
    }
    
    // Get related posts (same category, excluding current)
    const relatedPosts = await Blog.find({
      category: blog.category,
      _id: { $ne: blog._id }
    }).limit(3);
    
    return NextResponse.json({ 
      success: true, 
      data: { blog, relatedPosts } 
    });
  } catch (error) {
    console.error('Error fetching blog:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch blog' },
      { status: 500 }
    );
  }
}